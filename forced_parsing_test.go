package main

import (
	"testing"
	"time"
)

func TestGetNextTargetTime(t *testing.T) {
	tests := []struct {
		name     string
		input    time.Time
		expected time.Time
	}{
		{
			name:     "From 20:05 should go to 20:10",
			input:    time.Date(2024, 1, 1, 20, 5, 30, 0, time.UTC),
			expected: time.Date(2024, 1, 1, 20, 10, 0, 0, time.UTC),
		},
		{
			name:     "From 20:15 should go to 20:20",
			input:    time.Date(2024, 1, 1, 20, 15, 45, 0, time.UTC),
			expected: time.Date(2024, 1, 1, 20, 20, 0, 0, time.UTC),
		},
		{
			name:     "From 20:55 should go to 21:00",
			input:    time.Date(2024, 1, 1, 20, 55, 0, 0, time.UTC),
			expected: time.Date(2024, 1, 1, 21, 0, 0, 0, time.UTC),
		},
		{
			name:     "From 23:55 should go to 00:00 next day",
			input:    time.Date(2024, 1, 1, 23, 55, 0, 0, time.UTC),
			expected: time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getNextTargetTime(tt.input)
			if !result.Equal(tt.expected) {
				t.Errorf("getNextTargetTime() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestGetCurrentTargetTime(t *testing.T) {
	tests := []struct {
		name     string
		input    time.Time
		expected time.Time
		isZero   bool
	}{
		{
			name:     "20:10:30 is target time",
			input:    time.Date(2024, 1, 1, 20, 10, 30, 0, time.UTC),
			expected: time.Date(2024, 1, 1, 20, 10, 0, 0, time.UTC),
			isZero:   false,
		},
		{
			name:     "20:00:00 is target time",
			input:    time.Date(2024, 1, 1, 20, 0, 0, 0, time.UTC),
			expected: time.Date(2024, 1, 1, 20, 0, 0, 0, time.UTC),
			isZero:   false,
		},
		{
			name:   "20:15:30 is not target time",
			input:  time.Date(2024, 1, 1, 20, 15, 30, 0, time.UTC),
			isZero: true,
		},
		{
			name:   "20:05:30 is not target time",
			input:  time.Date(2024, 1, 1, 20, 5, 30, 0, time.UTC),
			isZero: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getCurrentTargetTime(tt.input)
			if tt.isZero {
				if !result.IsZero() {
					t.Errorf("getCurrentTargetTime() = %v, want zero time", result)
				}
			} else {
				if !result.Equal(tt.expected) {
					t.Errorf("getCurrentTargetTime() = %v, want %v", result, tt.expected)
				}
			}
		})
	}
}

func TestIsWithinForcedParsingWindow(t *testing.T) {
	beforeMs := 60000 // 1 minute before
	afterMs := 60000  // 1 minute after

	tests := []struct {
		name     string
		input    time.Time
		expected bool
	}{
		{
			name:     "20:09:30 should be within window (30s before 20:10)",
			input:    time.Date(2024, 1, 1, 20, 9, 30, 0, time.UTC),
			expected: true,
		},
		{
			name:     "20:10:30 should be within window (30s after 20:10)",
			input:    time.Date(2024, 1, 1, 20, 10, 30, 0, time.UTC),
			expected: true,
		},
		{
			name:     "20:08:30 should not be within window (1.5min before 20:10)",
			input:    time.Date(2024, 1, 1, 20, 8, 30, 0, time.UTC),
			expected: false,
		},
		{
			name:     "20:11:30 should not be within window (1.5min after 20:10)",
			input:    time.Date(2024, 1, 1, 20, 11, 30, 0, time.UTC),
			expected: false,
		},
		{
			name:     "20:05:00 should not be within window (not near target time)",
			input:    time.Date(2024, 1, 1, 20, 5, 0, 0, time.UTC),
			expected: false,
		},
		{
			name:     "19:59:30 should be within window (30s before 20:00)",
			input:    time.Date(2024, 1, 1, 19, 59, 30, 0, time.UTC),
			expected: true,
		},
		{
			name:     "20:00:30 should be within window (30s after 20:00)",
			input:    time.Date(2024, 1, 1, 20, 0, 30, 0, time.UTC),
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isWithinForcedParsingWindow(tt.input, beforeMs, afterMs)
			if result != tt.expected {
				t.Errorf("isWithinForcedParsingWindow() = %v, want %v", result, tt.expected)
			}
		})
	}
}
