package main

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// Config models application configuration loaded from JSON.
// It allows multi-account trading setups and moving flag-like options into a file.
type Config struct {
	Settings        SettingsConfig        `json:"settings"`
	Accounts        []AccountConfig       `json:"accounts"`
	Proxies         []string              `json:"proxies"`
	ProxiesEnabled  *bool                 `json:"proxies_enabled"`
	TelegramSignals TelegramSignalsConfig `json:"telegram_signals"`
}

type IntervalTuningConfig struct {
	Enabled            bool `json:"enabled"`
	CheckPeriodMinutes int  `json:"check_period_minutes"`
	TuneStepMs         int  `json:"tune_step_ms"`
	MaxRollbackCount   int  `json:"max_rollback_count"`
	MinIntervalMs      int  `json:"min_interval_ms"`
	MaxIntervalMs      int  `json:"max_interval_ms"`
}

type APIConfig struct {
	Playground string    `json:"playground"` // "dev" or "prod"
	URLs       URLConfig `json:"urls"`
}

type URLConfig struct {
	Dev  URLEndpoints `json:"dev"`
	Prod URLEndpoints `json:"prod"`
}

type URLEndpoints struct {
	AnnouncementsAPI string `json:"announcements_api"`
	MarketsAPI       string `json:"markets_api"`
	ServiceCenter    string `json:"service_center"`
}

type SettingsConfig struct {
	Cycle            bool                   `json:"cycle"`
	Trade            bool                   `json:"trade"`
	IntervalTuning   IntervalTuningConfig   `json:"interval_tuning"`
	AnnouncementsAPI AnnouncementsAPIConfig `json:"announcements_api"`
	MarketsAPI       MarketsAPIConfig       `json:"markets_api"`
	ServiceCenter    ServiceCenterConfig    `json:"service_center"`
	API              APIConfig              `json:"api"`
}

type ForcedParsingConfig struct {
	Enabled           bool `json:"enabled"`
	BeforeMs          int  `json:"before_ms"`           // Время до целевого момента в миллисекундах (например, 60000 для -1 минуты)
	AfterMs           int  `json:"after_ms"`            // Время после целевого момента в миллисекундах (например, 60000 для +1 минуты)
	RequestIntervalMs int  `json:"request_interval_ms"` // Интервал между дополнительными запросами в миллисекундах (например, 500)
}

type AnnouncementsAPIConfig struct {
	Enabled           bool                `json:"enabled"`
	CycleIntervalMs   int                 `json:"cycle_interval_ms"`
	MaxAttempts       int                 `json:"max_attempts"`
	MaxRetryAfterSec  int                 `json:"max_retry_after_sec"`
	PageDelayMs       int                 `json:"page_delay_ms"`
	PageDelayJitterMs int                 `json:"page_delay_jitter_ms"`
	Proxy             bool                `json:"proxy"`
	IntervalFixed     bool                `json:"interval_fixed,omitempty"`
	ForcedParsing     ForcedParsingConfig `json:"forced_parsing,omitempty"`
}

type MarketsAPIConfig struct {
	Enabled       bool                `json:"enabled"`
	IntervalMs    int                 `json:"interval_ms"`
	MaxAttempts   int                 `json:"max_attempts"`
	TimeoutSec    int                 `json:"timeout_sec"`
	Proxy         bool                `json:"proxy"`
	IntervalFixed bool                `json:"interval_fixed,omitempty"`
	ForcedParsing ForcedParsingConfig `json:"forced_parsing,omitempty"`
}

type ServiceCenterConfig struct {
	Enabled       bool                `json:"enabled"`
	IntervalMs    int                 `json:"interval_ms"`
	MaxAttempts   int                 `json:"max_attempts"`
	TimeoutSec    int                 `json:"timeout_sec"`
	Proxy         bool                `json:"proxy"`
	IntervalFixed bool                `json:"interval_fixed,omitempty"`
	ForcedParsing ForcedParsingConfig `json:"forced_parsing,omitempty"`
}

type AccountConfig struct {
	Exchange         string    `json:"exchange"`
	Name             string    `json:"name"`
	Enabled          bool      `json:"enabled"`
	APIKey           string    `json:"api_key"`
	APISecret        string    `json:"api_secret"`
	Direction        string    `json:"direction"`
	AmountUSDT       float64   `json:"amount_usdt"`
	Leverage         int       `json:"leverage"`
	TP               []float64 `json:"tp"`
	SL               []float64 `json:"sl"`
	TrailingEnabled  bool      `json:"trailing_enabled"`
	TrailingPercent  float64   `json:"trailing_percent"`
	TelegramEnabled  bool      `json:"telegram_enabled"`
	TelegramLogsChat int64     `json:"telegram_logs_chat"`
	TelegramBotToken string    `json:"telegram_bot_token"`
}

type TelegramSignalsConfig struct {
	Enabled  bool   `json:"enabled"`
	ChatID   int64  `json:"telegram_signals_chat"`
	BotToken string `json:"telegram_bot_token"`
}

// LoadConfig reads JSON from the given path. If path is empty, it looks for
// a file named config.json in the current working directory. Returns (nil, nil)
// when no config file is present.
func LoadConfig(path string) (*Config, error) {
	if strings.TrimSpace(path) == "" {
		cwd, _ := os.Getwd()
		path = filepath.Join(cwd, "config.json")
		if _, err := os.Stat(path); errors.Is(err, os.ErrNotExist) {
			return nil, nil
		}
	}
	b, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}
	var cfg Config
	if err := json.Unmarshal(b, &cfg); err != nil {
		return nil, err
	}
	expandEnvInConfig(&cfg)
	return &cfg, nil
}

// expandEnvInConfig replaces ${VAR} and $VAR with values from the environment
// in all string fields that may contain secrets or endpoints.
func expandEnvInConfig(cfg *Config) {
	for i := range cfg.Proxies {
		cfg.Proxies[i] = os.ExpandEnv(cfg.Proxies[i])
	}
	for i := range cfg.Accounts {
		cfg.Accounts[i].APIKey = os.ExpandEnv(cfg.Accounts[i].APIKey)
		cfg.Accounts[i].APISecret = os.ExpandEnv(cfg.Accounts[i].APISecret)
		cfg.Accounts[i].TelegramBotToken = os.ExpandEnv(cfg.Accounts[i].TelegramBotToken)
	}
	cfg.TelegramSignals.BotToken = os.ExpandEnv(cfg.TelegramSignals.BotToken)
}

// SaveConfigIntervals saves only interval changes while preserving environment variables
func SaveConfigIntervals(cfg *Config, path string) error {
	if strings.TrimSpace(path) == "" {
		cwd, _ := os.Getwd()
		path = filepath.Join(cwd, "config.json")
	}

	// Read original config file
	originalData, err := os.ReadFile(path)
	if err != nil {
		return fmt.Errorf("failed to read original config: %w", err)
	}

	// Parse original config as map to preserve structure
	var originalMap map[string]interface{}
	if err := json.Unmarshal(originalData, &originalMap); err != nil {
		return fmt.Errorf("failed to parse original config: %w", err)
	}

	// Update only interval-related fields
	if settings, ok := originalMap["settings"].(map[string]interface{}); ok {
		if announcementsAPI, ok := settings["announcements_api"].(map[string]interface{}); ok {
			announcementsAPI["cycle_interval_ms"] = cfg.Settings.AnnouncementsAPI.CycleIntervalMs
			if cfg.Settings.AnnouncementsAPI.IntervalFixed {
				announcementsAPI["interval_fixed"] = true
			}
		}
		if marketsAPI, ok := settings["markets_api"].(map[string]interface{}); ok {
			marketsAPI["interval_ms"] = cfg.Settings.MarketsAPI.IntervalMs
			if cfg.Settings.MarketsAPI.IntervalFixed {
				marketsAPI["interval_fixed"] = true
			}
		}
		if serviceCenter, ok := settings["service_center"].(map[string]interface{}); ok {
			serviceCenter["interval_ms"] = cfg.Settings.ServiceCenter.IntervalMs
			if cfg.Settings.ServiceCenter.IntervalFixed {
				serviceCenter["interval_fixed"] = true
			}
		}
	}

	// Create backup before saving
	backupPath := fmt.Sprintf("%s.backup.%s", path, time.Now().Format("20060102-150405"))
	if err := os.WriteFile(backupPath, originalData, 0644); err != nil {
		return fmt.Errorf("failed to create backup: %w", err)
	}

	// Marshal updated config
	data, err := json.MarshalIndent(originalMap, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	// Write to temporary file first
	tmpPath := path + ".tmp"
	if err := os.WriteFile(tmpPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write temp config: %w", err)
	}

	// Atomic rename
	if err := os.Rename(tmpPath, path); err != nil {
		_ = os.Remove(tmpPath) // Clean up temp file
		return fmt.Errorf("failed to rename temp config: %w", err)
	}

	return nil
}

// GetCurrentURLs returns the appropriate URL endpoints based on playground setting
func (cfg *Config) GetCurrentURLs() URLEndpoints {
	if cfg.Settings.API.Playground == "dev" {
		return cfg.Settings.API.URLs.Dev
	}
	return cfg.Settings.API.URLs.Prod
}

// GetAnnouncementsAPI returns the announcements API URL for current environment
func (cfg *Config) GetAnnouncementsAPI() string {
	return cfg.GetCurrentURLs().AnnouncementsAPI
}

// GetMarketsAPI returns the markets API URL for current environment
func (cfg *Config) GetMarketsAPI() string {
	return cfg.GetCurrentURLs().MarketsAPI
}

// GetServiceCenterURL returns the service center URL for current environment
func (cfg *Config) GetServiceCenterURL() string {
	return cfg.GetCurrentURLs().ServiceCenter
}
