# See https://help.github.com/ignore-files/ for more about ignoring files.

# private
/private/
/.tmp/
/.data
tasks/
candles/
.venv/
venv/

# dependencies
node_modules/
/invest-api/node_modules/

# testing
coverage/

# production
build/

# false folder when use npm i --no-cache
false/

# misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
package-lock.json
yarn-debug.log*
yarn-error.log*
yarn.lock


.vscode
*.snap
*.xlog
.rocks/
.rocks/
.rocks
__pycache__
temp/
tmp/
npm-debug.log*
