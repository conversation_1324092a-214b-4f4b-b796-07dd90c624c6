# Алгоритм работы системы upbit-listing

## Основная архитектура системы

```mermaid
graph TB
    A[Запуск main.go] --> B[Загрузка .env файла]
    B --> C[Переопределение конфигурации из переменных окружения]
    C --> D[Загрузка config.json]
    D --> E[Применение настроек из конфигурации]
    E --> F[Построение пула HTTP клиентов]
    F --> G{Режим работы?}
    
    G -->|Не циклический| H[Запуск источников один раз]
    G -->|Циклический| I[Запуск независимых циклов]
    
    H --> J[Завершение работы]
    I --> K[Мониторинг и отчетность]
```

## Построение HTTP клиентов

```mermaid
graph TD
    A[buildHTTPClients] --> B[Инициализация статистики прокси]
    B --> C[Добавление прямого клиента]
    C --> D{Прокси включены?}
    
    D -->|Нет| E[Только прямой клиент]
    D -->|Да| F[Парсинг конфигурации прокси]
    
    F --> G{Формат прокси?}
    G -->|ip:port:user:pass| H[Схема HTTP по умолчанию]
    G -->|scheme:ip:port:user:pass| I[Использование указанной схемы]
    
    H --> J[Создание транспортного слоя]
    I --> J
    J --> K[Создание HTTP клиента]
    K --> L[Добавление в пул клиентов]
    L --> M[Инициализация статистики для прокси]
    
    E --> N[Логирование результатов]
    M --> N
```

## Основной цикл работы (циклический режим)

```mermaid
graph TD
    A[Циклический режим] --> B{Источник новостей включен?}
    B -->|Да| C[startClientCycles]
    B -->|Нет| D[Пропуск новостей]
    
    C --> E[Запуск независимых циклов для каждого HTTP клиента]
    E --> F[Запуск цикла статистики]
    F --> G[Запуск цикла очистки резервных копий]
    
    D --> H{Источник pairs_diff включен?}
    H -->|Да| I{Использовать прокси?}
    H -->|Нет| J[Пропуск pairs_diff]
    
    I -->|Да| K[startPairsDiffClientCycles]
    I -->|Нет| L[Запуск одиночного клиента]
    
    K --> M[Запуск независимых циклов для pairs_diff]
    L --> N[Запуск тикера с интервалом]
    
    J --> O[Основной цикл мониторинга]
    M --> O
    N --> O
    
    O --> P[Проверка статуса каждые 30 секунд]
    P --> O
```

## Цикл клиента для новостей

```mermaid
graph TD
    A[runClientIteration] --> B[Создание HTTP запроса]
    B --> C[Установка заголовков]
    C --> D[Выполнение запроса с таймаутом]
    D --> E{Статус ответа?}
    
    E -->|Ошибка| F[Обновление статистики прокси - ошибка]
    E -->|Успех| G[Чтение тела ответа]
    
    F --> H[Логирование ошибки]
    G --> I[Декодирование JSON]
    
    I --> J{API успешен?}
    J -->|Нет| K[Обновление статистики - ошибка]
    J -->|Да| L[Обработка данных]
    
    K --> H
    L --> M[Проверка существующих записей]
    M --> N[Фильтрация новых объявлений]
    N --> O{Есть новые элементы?}
    
    O -->|Нет| P[Обновление статистики - успех]
    O -->|Да| Q[Добавление в кэш]
    
    P --> R[Завершение итерации]
    Q --> S[Отправка Telegram сигналов]
    S --> T[Обработка торговли]
    T --> P
```

## Обработка торговли

```mermaid
graph TD
    A[handleTradingWithAccounts] --> B{Есть новые листинги?}
    B -->|Нет| C[Завершение]
    B -->|Да| D[Итерация по элементам]
    
    D --> E{Элемент - листинг?}
    E -->|Нет| D
    E -->|Да| F[Извлечение символа]
    
    F --> G[Итерация по аккаунтам]
    G --> H{Аккаунт включен?}
    H -->|Нет| G
    H -->|Да| I{Тестовый режим?}
    
    I -->|Да| J[Запись тестовых событий]
    I -->|Нет| K{Биржа?}
    
    J --> L[Продолжение цикла]
    K -->|Bybit| M[Создание клиента Bybit]
    K -->|BingX| N[Логирование - не реализовано]
    
    M --> O[Получение информации об инструменте]
    O --> P[Получение последней цены]
    P --> Q[Расчет количества]
    Q --> R[Установка кредитного плеча]
    R --> S[Размещение рыночного ордера]
    
    S --> T[Получение средней цены позиции]
    T --> U[Установка TP/SL для позиции]
    U --> V[Размещение дополнительных TP ордеров]
    V --> W[Размещение дополнительных SL ордеров]
    W --> X[Отправка логов в Telegram]
    
    N --> L
    X --> L
    L --> G
```

## Управление статистикой прокси

```mermaid
graph TD
    A[updateProxyStats] --> B{Успешный запрос?}
    B -->|Да| C[Увеличение счетчика успехов]
    C --> D[Сброс счетчика ошибок]
    D --> E[Обновление времени последнего успеха]
    E --> F[Обновление времени последнего обновления]
    F --> G[Логирование успеха]
    
    B -->|Нет| H[Увеличение счетчика ошибок]
    H --> I[Обновление времени последней ошибки]
    I --> J[Увеличение счетчика последовательных ошибок]
    J --> K[Проверка необходимости отключения]
    
    K --> L{Должен быть отключен?}
    L -->|Да| M[disableProxy]
    L -->|Нет| N[Логирование ошибки]
    
    M --> O[Отключение прокси]
    O --> P[Удаление из config.json]
    P --> Q[Создание резервной копии]
    
    G --> R[Завершение]
    N --> R
    Q --> R
```

## Алгоритм отключения прокси

```mermaid
graph TD
    A[shouldDisableProxy] --> B{Достигнут лимит последовательных ошибок?}
    B -->|Да| C[Отключить прокси]
    B -->|Нет| D{Минимальное количество попыток?}
    
    D -->|Нет| E[Не отключать]
    D -->|Да| F[Расчет процента ошибок]
    
    F --> G{Процент ошибок > 70%?}
    G -->|Да| C
    G -->|Нет| H[Расчет процента успехов]
    
    H --> I{Процент успехов < 30%?}
    I -->|Да| C
    I -->|Нет| E
    
    C --> J[Возврат: отключить]
    E --> K[Возврат: не отключать]
```

## Обработка pairs_diff

```mermaid
graph TD
    A[runPairsDiffClientIteration] --> B[Засечка времени начала]
    B --> C[Использование конкретного HTTP клиента]
    C --> D[Запрос списка рынков Upbit]
    D --> E{Успешный запрос?}
    
    E -->|Нет| F[Обновление статистики - ошибка]
    E -->|Да| G[Обновление статистики - успех]
    
    F --> H[Возврат ошибки]
    G --> I[Расчет времени с последнего обновления]
    I --> J[Логирование результатов]
    
    J --> K[Поиск новых рынков]
    K --> L{Обнаружены новые рынки?}
    
    L -->|Нет| M[Логирование - новых рынков нет]
    L -->|Да| N[Добавление новых пар в listing_api.json]
    
    M --> O[Завершение итерации]
    N --> P[Обновление кэша рынков]
    P --> Q[Атомарное обновление известных рынков]
    Q --> O
```

## Система резервного копирования

```mermaid
graph TD
    A[cleanupOldBackups] --> B[Поиск файлов резервных копий]
    B --> C{Количество копий <= 5?}
    C -->|Да| D[Оставить все копии]
    C -->|Нет| E[Сортировка по времени модификации]
    
    E --> F[Вычисление количества для удаления]
    F --> G[Удаление старых копий]
    G --> H[Логирование удаления]
    
    D --> I[Завершение]
    H --> I
```

## Обработка конфигурации

```mermaid
graph TD
    A[LoadConfig] --> B[Поиск config.json]
    B --> C{Файл найден?}
    C -->|Нет| D[Возврат ошибки]
    C -->|Да| E[Чтение файла]
    
    E --> F[Парсинг JSON]
    F --> G{JSON валиден?}
    G -->|Нет| H[Возврат ошибки парсинга]
    G -->|Да| I[Валидация структуры]
    
    I --> J{Структура корректна?}
    J -->|Нет| K[Возврат ошибки валидации]
    J -->|Да| L[Возврат конфигурации]
```

## Система логирования и мониторинга

```mermaid
graph TD
    A[printProxyStats] --> B[Блокировка мьютекса для чтения]
    B --> C[Итерация по всем прокси]
    C --> D{Есть статистика?}
    
    D -->|Нет| E[Пропуск прокси]
    D -->|Да| F[Расчет процентов успеха/ошибок]
    
    F --> G[Определение статуса]
    G --> H[Расчет времени с последнего обновления]
    H --> I[Логирование статистики]
    
    E --> J{Следующий прокси?}
    I --> J
    
    J -->|Да| C
    J -->|Нет| K[Разблокировка мьютекса]
    K --> L[Завершение отчета]
```

## Основные компоненты системы

- **HTTP клиенты**: Пул клиентов (прямой + прокси) с ротацией
- **Статистика прокси**: Отслеживание производительности и автоматическое отключение
- **Кэширование**: Сохранение данных в JSON файлах с атомарной записью
- **Торговля**: Автоматическое размещение ордеров на Bybit/BingX
- **Telegram интеграция**: Отправка уведомлений о новых листингах
- **Мониторинг**: Непрерывное отслеживание состояния системы
- **Резервное копирование**: Автоматическое создание и очистка бэкапов

## Ключевые алгоритмы

1. **Параллельная обработка**: Каждый HTTP клиент работает независимо
2. **Адаптивное управление прокси**: Автоматическое отключение неэффективных прокси
3. **Атомарные операции**: Безопасная запись файлов через временные файлы
4. **Экспоненциальная задержка**: Повторные попытки с увеличивающимися интервалами
5. **Jitter**: Случайные вариации в задержках для избежания синхронизации
