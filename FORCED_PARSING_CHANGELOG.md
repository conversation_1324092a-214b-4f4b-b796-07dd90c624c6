# Changelog: Форсированный парсинг

## Добавленная функциональность

### 1. Новые структуры конфигурации

**В `config.go`:**
- Добавлена структура `ForcedParsingConfig` с полями:
  - `Enabled` (bool) - включение/выключение форсированного парсинга
  - `BeforeMs` (int) - время до целевого момента в миллисекундах
  - `AfterMs` (int) - время после целевого момента в миллисекундах  
  - `RequestIntervalMs` (int) - интервал между дополнительными запросами

- Добавлено поле `ForcedParsing` в структуры:
  - `AnnouncementsAPIConfig`
  - `MarketsAPIConfig`
  - `ServiceCenterConfig`

### 2. Обновленная конфигурация

**В `config.json`:**
- Добавлены настройки `forced_parsing` для всех источников данных
- Значения по умолчанию:
  - `enabled: false` (отключено)
  - `before_ms: 60000` (1 минута до)
  - `after_ms: 60000` (1 минута после)
  - `request_interval_ms: 500` (запрос каждые 500мс)

### 3. Основная логика

**В `main.go`:**
- `getNextTargetTime()` - определение следующего целевого времени
- `getCurrentTargetTime()` - определение текущего целевого времени
- `isWithinForcedParsingWindow()` - проверка нахождения в окне форсированного парсинга
- `startForcedParsingWorker()` - воркер для мониторинга времени и запуска форсированного парсинга

### 4. Интеграция с существующими циклами

- Интеграция в `startClientCycles()` для announcements_api
- Интеграция в `startPairsDiffClientCycles()` для markets_api  
- Интеграция в `startServiceCenterClientCycles()` для service_center

### 5. Тестирование

**Файлы:**
- `forced_parsing_test.go` - юнит-тесты для основных функций
- `cmd/test-forced-parsing/main.go` - утилита для демонстрации работы
- `config-forced-parsing-demo.json` - демо-конфигурация

### 6. Документация

- `FORCED_PARSING.md` - подробная документация функциональности
- Обновлен `README.md` с информацией о новой функции

## Принцип работы

1. **Целевые времена**: xx:00, xx:10, xx:20, xx:30, xx:40, xx:50
2. **Временное окно**: от `before_ms` до `after_ms` относительно целевого времени
3. **Дополнительные запросы**: выполняются с интервалом `request_interval_ms`
4. **Логирование**: начало и окончание форсированного парсинга

## Пример использования

```json
{
  "settings": {
    "announcements_api": {
      "enabled": true,
      "forced_parsing": {
        "enabled": true,
        "before_ms": 60000,
        "after_ms": 60000,
        "request_interval_ms": 500
      }
    }
  }
}
```

Для времени 20:10:00 форсированный парсинг будет активен с 20:09:00 до 20:11:00 с запросами каждые 500мс.

## Тестирование

```bash
# Запуск юнит-тестов
go test -v forced_parsing_test.go main.go config.go

# Демонстрация работы
go run ./cmd/test-forced-parsing

# Тест с демо-конфигурацией
./upbit-listing -config=config-forced-parsing-demo.json
```

## Совместимость

- ✅ Полностью обратно совместимо
- ✅ Работает с существующими конфигурациями
- ✅ Опциональная функциональность (по умолчанию отключена)
- ✅ Не влияет на производительность при отключении
