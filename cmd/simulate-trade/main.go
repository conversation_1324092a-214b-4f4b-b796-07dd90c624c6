package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"sort"
	"strings"
	"time"
)

func printStartupBanner(name string) {
	fmt.Println("# " + name)
	fmt.Println("Version: 0.0.1")
	fmt.Println("Build: " + buildDate)
	fmt.Println("Commit: " + commitHash)
	fmt.Println("Author: https://t.me/suenot")
	fmt.Println("---")
	flag.PrintDefaults()
}

var buildDate = "unknown"
var commitHash = "unknown"

func main() {
	var (
		symbolBase   string
		direction    string
		amountUSDT   float64
		leverage     int
		tpStr, slStr string
		exchange     string
	)

	flag.StringVar(&symbolBase, "symbol", "ETH", "base symbol to simulate listing for (e.g. ETH)")
	flag.StringVar(&direction, "direction", "", "order direction: long|short (required)")
	flag.Float64Var(&amountUSDT, "amount-usdt", 0, "order notional size in USDT (required)")
	flag.IntVar(&leverage, "leverage", 1, "leverage to set for the symbol on Bybit (>=1)")
	flag.StringVar(&tpStr, "tp", "", "take profit percentages, comma-separated (e.g. 2,4,6). Size split equally")
	flag.StringVar(&slStr, "sl", "", "stop loss percentages, comma-separated (e.g. 1.5,3). Size split equally")
	flag.StringVar(&exchange, "exchange", "bybit", "exchange to trade on: bybit|bingx")
	printStartupBanner("simulate-trade")
	flag.Parse()

	loadDotEnv()

	if direction != "long" && direction != "short" {
		log.Fatalf("invalid -direction: must be long or short")
	}
	if amountUSDT <= 0 {
		log.Fatalf("-amount-usdt must be > 0")
	}
	if leverage < 1 {
		log.Fatalf("-leverage must be >= 1")
	}
	useBybit := strings.EqualFold(exchange, "bybit")
	useBingx := strings.EqualFold(exchange, "bingx")
	if !useBybit && !useBingx {
		log.Fatalf("unsupported -exchange: %s (use bybit or bingx)", exchange)
	}
	var bybit *bybitClient
	if useBybit {
		if os.Getenv("BYBIT_API_KEY") == "" || os.Getenv("BYBIT_API_SECRET") == "" {
			log.Fatalf("BYBIT_API_KEY/BYBIT_API_SECRET are required for bybit")
		}
		apiKey := os.Getenv("BYBIT_API_KEY")
		apiSecret := os.Getenv("BYBIT_API_SECRET")
		bybit = newBybitClient(apiKey, apiSecret)
	}
	if useBingx && bybit == nil {
		// use Bybit public endpoints (no keys) to fetch price/filters as pragmatic default
		bybit = newBybitClient("", "")
	}
	var bingx *bingxClient
	if useBingx {
		if os.Getenv("BINGX_API_KEY") == "" || os.Getenv("BINGX_API_SECRET") == "" {
			log.Fatalf("BINGX_API_KEY/BINGX_API_SECRET are required for bingx")
		}
		bingx = newBingxClient(os.Getenv("BINGX_API_KEY"), os.Getenv("BINGX_API_SECRET"))
	}

	base := strings.ToUpper(strings.TrimSpace(symbolBase))
	symbol := base + "USDT"
	category := "linear"
	bingxSymbol := base + "-USDT"

	log.Printf("simulate: listing=true received for base=%s -> trading %s", base, symbol)

	var price float64
	var qtyStep, minQty, priceStep float64
	if useBybit || useBingx {
		info, err := bybit.getInstrument(category, symbol)
		if err != nil {
			log.Fatalf("instrument not tradable on Bybit (used for filters): %v", err)
		}
		p, err := bybit.getLastPrice(category, symbol)
		if err != nil || p <= 0 {
			log.Fatalf("failed to get price (bybit public): %v", err)
		}
		price = p
		qtyStep = info.LotSizeFilter.QtyStep
		minQty = info.LotSizeFilter.MinOrderQty
		priceStep = info.PriceFilter.TickSize
	}

	qty := amountUSDT / price
	qty = floorToStep(qty, qtyStep)
	if qty < minQty {
		log.Fatalf("qty %.8f < min %.8f", qty, minQty)
	}

	if useBybit {
		if err := bybit.setLeverage(category, symbol, leverage); err != nil {
			log.Printf("warn: set leverage failed: %v", err)
		}
	}

	side := "Buy"
	if direction == "short" {
		side = "Sell"
	}
	if useBybit {
		orderID, err := bybit.placeMarketOrder(category, symbol, side, qty)
		if err != nil {
			log.Fatalf("market order failed: %v", err)
		}
		log.Printf("opened %s %s qty=%.6f orderID=%s", side, symbol, qty, orderID)
	}
	if useBingx {
		bingxSide := "BUY"
		if strings.EqualFold(side, "Sell") {
			bingxSide = "SELL"
		}
		orderID, err := bingx.placeMarketOrder(bingxSymbol, bingxSide, qty)
		if err != nil {
			log.Fatalf("bingx market order failed: %v", err)
		}
		log.Printf("bingx opened %s %s qty=%.6f orderID=%s", bingxSide, bingxSymbol, qty, orderID)
	}

	avgPrice := price
	if useBybit {
		if pos, err := bybit.getPosition(category, symbol); err == nil && pos.AvgPrice > 0 {
			avgPrice = pos.AvgPrice
		}
	}

	tpList := parseFloatList(tpStr)
	slList := parseFloatList(slStr)

	// Position-linked TP/SL: attach closest SL, and only single TP if provided (Bybit only)
	if useBybit && (len(slList) > 0 || len(tpList) == 1) {
		var tpPtr *float64
		var slPtr *float64
		if len(tpList) == 1 {
			p := tpList[0]
			var tp float64
			if direction == "long" {
				tp = avgPrice * (1 + p/100)
			} else {
				tp = avgPrice * (1 - p/100)
			}
			t := roundToStep(tp, priceStep)
			tpPtr = &t
		}
		if len(slList) > 0 {
			closest := slList[0]
			for _, v := range slList {
				if v < closest {
					closest = v
				}
			}
			var sl float64
			if direction == "long" {
				sl = avgPrice * (1 - closest/100)
			} else {
				sl = avgPrice * (1 + closest/100)
			}
			s := roundToStep(sl, priceStep)
			slPtr = &s
		}
		if err := bybit.setPositionTradingStop(category, symbol, tpPtr, slPtr); err != nil {
			log.Printf("warn: failed to set position TP/SL: %v", err)
		}
	}

	if useBybit && len(tpList) > 1 {
		portion := qty / float64(len(tpList))
		portion = floorToStep(portion, qtyStep)
		for _, p := range tpList {
			var tpPrice float64
			if direction == "long" {
				tpPrice = avgPrice * (1 + p/100)
			} else {
				tpPrice = avgPrice * (1 - p/100)
			}
			tpPrice = roundToStep(tpPrice, priceStep)
			opp := oppositeSide(side)
			if _, err := bybit.placeLimitReduceOnly(category, symbol, opp, portion, tpPrice); err != nil {
				log.Printf("TP failed p=%.3f: %v", p, err)
			}
		}
	}
	if useBybit && len(slList) > 1 {
		portion := qty / float64(len(slList))
		portion = floorToStep(portion, qtyStep)
		for _, p := range slList {
			var slTrig float64
			if direction == "long" {
				slTrig = avgPrice * (1 - p/100)
			} else {
				slTrig = avgPrice * (1 + p/100)
			}
			slTrig = roundToStep(slTrig, priceStep)
			opp := oppositeSide(side)
			if _, err := bybit.placeStopMarketReduceOnly(category, symbol, opp, portion, slTrig, direction); err != nil {
				log.Printf("SL failed p=%.3f: %v", p, err)
			}
		}
	}

	// BingX: place conditional orders for TP/SL (no position-level edit). For single or multiple, use reduce-only market exits.
	if useBingx {
		bingxCloseSide := "SELL"
		posSide := "LONG"
		if strings.EqualFold(direction, "short") {
			bingxCloseSide = "BUY"
			posSide = "SHORT"
		}
		if len(tpList) > 0 {
			portion := qty
			if len(tpList) > 1 {
				portion = floorToStep(qty/float64(len(tpList)), qtyStep)
			}
			for _, p := range tpList {
				var stopPrice float64
				if strings.EqualFold(direction, "long") {
					stopPrice = avgPrice * (1 + p/100)
				} else {
					stopPrice = avgPrice * (1 - p/100)
				}
				stopPrice = roundToStep(stopPrice, priceStep)
				if _, err := bingx.placeConditionalMarket(bingxSymbol, bingxCloseSide, posSide, "TAKE_PROFIT_MARKET", portion, stopPrice); err != nil {
					log.Printf("bingx TP failed p=%.3f: %v", p, err)
				}
			}
		}
		if len(slList) > 0 {
			portion := qty
			if len(slList) > 1 {
				portion = floorToStep(qty/float64(len(slList)), qtyStep)
			}
			for _, p := range slList {
				var stopPrice float64
				if strings.EqualFold(direction, "long") {
					stopPrice = avgPrice * (1 - p/100)
				} else {
					stopPrice = avgPrice * (1 + p/100)
				}
				stopPrice = roundToStep(stopPrice, priceStep)
				if _, err := bingx.placeConditionalMarket(bingxSymbol, bingxCloseSide, posSide, "STOP_MARKET", portion, stopPrice); err != nil {
					log.Printf("bingx SL failed p=%.3f: %v", p, err)
				}
			}
		}
	}
	log.Printf("simulation complete for %s", symbol)
}

// --- helpers copied from root main for standalone run ---

func loadDotEnv() {
	f, err := os.Open(".env")
	if err != nil {
		return
	}
	defer f.Close()
	data, err := io.ReadAll(f)
	if err != nil {
		return
	}
	lines := strings.Split(string(data), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		eq := strings.IndexByte(line, '=')
		if eq <= 0 {
			continue
		}
		key := strings.TrimSpace(line[:eq])
		val := strings.TrimSpace(line[eq+1:])
		val = strings.Trim(val, "\"'")
		_ = os.Setenv(key, val)
	}
}

func parseFloatList(s string) []float64 {
	var out []float64
	if strings.TrimSpace(s) == "" {
		return out
	}
	parts := strings.Split(s, ",")
	for _, p := range parts {
		p = strings.TrimSpace(p)
		if p == "" {
			continue
		}
		var v float64
		if _, err := fmt.Sscanf(p, "%f", &v); err == nil {
			if v < 0 {
				v = -v
			}
			out = append(out, v)
		}
	}
	return out
}

func oppositeSide(side string) string {
	if side == "Buy" {
		return "Sell"
	}
	return "Buy"
}

func floorToStep(value, step float64) float64 {
	if step <= 0 {
		return value
	}
	n := int(value / step)
	return float64(n) * step
}

func roundToStep(value, step float64) float64 {
	if step <= 0 {
		return value
	}
	n := int((value / step) + 0.5)
	return float64(n) * step
}

type bybitClient struct {
	apiKey    string
	apiSecret string
	hc        *http.Client
}

func newBybitClient(key, secret string) *bybitClient {
	return &bybitClient{apiKey: key, apiSecret: secret, hc: &http.Client{Timeout: 15 * time.Second}}
}

const bybitBase = "https://api.bybit.com"

func (c *bybitClient) signedPOST(path string, body string) ([]byte, error) {
	timestamp := fmt.Sprintf("%d", time.Now().UnixMilli())
	recvWindow := "5000"
	preSign := timestamp + c.apiKey + recvWindow + body
	mac := hmac.New(sha256.New, []byte(c.apiSecret))
	_, _ = mac.Write([]byte(preSign))
	signature := hex.EncodeToString(mac.Sum(nil))

	req, err := http.NewRequest(http.MethodPost, bybitBase+path, strings.NewReader(body))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-BAPI-API-KEY", c.apiKey)
	req.Header.Set("X-BAPI-SIGN", signature)
	req.Header.Set("X-BAPI-TIMESTAMP", timestamp)
	req.Header.Set("X-BAPI-RECV-WINDOW", recvWindow)

	res, err := c.hc.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	b, _ := io.ReadAll(res.Body)
	if res.StatusCode/100 != 2 {
		return nil, fmt.Errorf("bybit %s: %s", res.Status, string(b))
	}
	return b, nil
}

func (c *bybitClient) publicGET(path string, query string) ([]byte, error) {
	url := bybitBase + path
	if query != "" {
		url += "?" + query
	}
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	res, err := c.hc.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	b, _ := io.ReadAll(res.Body)
	if res.StatusCode/100 != 2 {
		return nil, fmt.Errorf("bybit %s: %s", res.Status, string(b))
	}
	return b, nil
}

type bybitInstrumentInfo struct {
	Symbol        string `json:"symbol"`
	LotSizeFilter struct {
		MinOrderQty float64 `json:"minOrderQty,string"`
		QtyStep     float64 `json:"qtyStep,string"`
	} `json:"lotSizeFilter"`
	PriceFilter struct {
		TickSize float64 `json:"tickSize,string"`
	} `json:"priceFilter"`
}

func (c *bybitClient) getInstrument(category, symbol string) (*bybitInstrumentInfo, error) {
	b, err := c.publicGET("/v5/market/instruments-info", fmt.Sprintf("category=%s&symbol=%s", category, symbol))
	if err != nil {
		return nil, err
	}
	var resp struct {
		RetCode int    `json:"retCode"`
		RetMsg  string `json:"retMsg"`
		Result  struct {
			List []bybitInstrumentInfo `json:"list"`
		} `json:"result"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return nil, err
	}
	if resp.RetCode != 0 || len(resp.Result.List) == 0 {
		return nil, fmt.Errorf("bybit: instrument not found: %s (%s)", symbol, resp.RetMsg)
	}
	info := resp.Result.List[0]
	return &info, nil
}

func (c *bybitClient) getLastPrice(category, symbol string) (float64, error) {
	b, err := c.publicGET("/v5/market/tickers", fmt.Sprintf("category=%s&symbol=%s", category, symbol))
	if err != nil {
		return 0, err
	}
	var resp struct {
		RetCode int    `json:"retCode"`
		RetMsg  string `json:"retMsg"`
		Result  struct {
			List []struct {
				LastPrice float64 `json:"lastPrice,string"`
			} `json:"list"`
		} `json:"result"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return 0, err
	}
	if resp.RetCode != 0 || len(resp.Result.List) == 0 {
		return 0, fmt.Errorf("bybit: ticker not found: %s (%s)", symbol, resp.RetMsg)
	}
	return resp.Result.List[0].LastPrice, nil
}

func (c *bybitClient) setLeverage(category, symbol string, leverage int) error {
	body := fmt.Sprintf(`{"category":"%s","symbol":"%s","buyLeverage":"%d","sellLeverage":"%d"}`, category, symbol, leverage, leverage)
	_, err := c.signedPOST("/v5/position/set-leverage", body)
	return err
}

func (c *bybitClient) placeMarketOrder(category, symbol, side string, qty float64) (string, error) {
	body := fmt.Sprintf(`{"category":"%s","symbol":"%s","side":"%s","orderType":"Market","qty":"%.8f","timeInForce":"IOC"}`, category, symbol, side, qty)
	b, err := c.signedPOST("/v5/order/create", body)
	if err != nil {
		return "", err
	}
	var resp struct {
		RetCode int    `json:"retCode"`
		RetMsg  string `json:"retMsg"`
		Result  struct {
			OrderId string `json:"orderId"`
		} `json:"result"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return "", err
	}
	if resp.RetCode != 0 {
		return "", fmt.Errorf("bybit order error: %s", resp.RetMsg)
	}
	return resp.Result.OrderId, nil
}

func (c *bybitClient) placeLimitReduceOnly(category, symbol, side string, qty, price float64) (string, error) {
	body := fmt.Sprintf(`{"category":"%s","symbol":"%s","side":"%s","orderType":"Limit","qty":"%.8f","price":"%.8f","timeInForce":"GTC","reduceOnly":true}`, category, symbol, side, qty, price)
	b, err := c.signedPOST("/v5/order/create", body)
	if err != nil {
		return "", err
	}
	var resp struct {
		RetCode int    `json:"retCode"`
		RetMsg  string `json:"retMsg"`
		Result  struct {
			OrderId string `json:"orderId"`
		} `json:"result"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return "", err
	}
	if resp.RetCode != 0 {
		return "", fmt.Errorf("bybit order error: %s", resp.RetMsg)
	}
	return resp.Result.OrderId, nil
}

func (c *bybitClient) placeStopMarketReduceOnly(category, symbol, side string, qty, triggerPrice float64, orderDirection string) (string, error) {
	triggerDirection := 2
	if orderDirection == "short" {
		triggerDirection = 1
	}
	body := fmt.Sprintf(`{"category":"%s","symbol":"%s","side":"%s","orderType":"Market","qty":"%.8f","reduceOnly":true,"triggerDirection":%d,"triggerPrice":"%.8f","timeInForce":"GTC"}`, category, symbol, side, qty, triggerDirection, triggerPrice)
	b, err := c.signedPOST("/v5/order/create", body)
	if err != nil {
		return "", err
	}
	var resp struct {
		RetCode int    `json:"retCode"`
		RetMsg  string `json:"retMsg"`
		Result  struct {
			OrderId string `json:"orderId"`
		} `json:"result"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return "", err
	}
	if resp.RetCode != 0 {
		return "", fmt.Errorf("bybit order error: %s", resp.RetMsg)
	}
	return resp.Result.OrderId, nil
}

type bybitPosition struct {
	AvgPrice float64
}

func (c *bybitClient) getPosition(category, symbol string) (*bybitPosition, error) {
	b, err := c.signedPOST("/v5/position/list", fmt.Sprintf(`{"category":"%s","symbol":"%s"}`, category, symbol))
	if err != nil {
		return nil, err
	}
	var resp struct {
		RetCode int    `json:"retCode"`
		RetMsg  string `json:"retMsg"`
		Result  struct {
			List []struct {
				AvgPrice float64 `json:"avgPrice,string"`
			} `json:"list"`
		} `json:"result"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return nil, err
	}
	if resp.RetCode != 0 || len(resp.Result.List) == 0 {
		return nil, fmt.Errorf("bybit position error: %s", resp.RetMsg)
	}
	return &bybitPosition{AvgPrice: resp.Result.List[0].AvgPrice}, nil
}

func (c *bybitClient) setPositionTradingStop(category, symbol string, tp, sl *float64) error {
	parts := []string{fmt.Sprintf("\"category\":\"%s\"", category), fmt.Sprintf("\"symbol\":\"%s\"", symbol)}
	if tp != nil {
		parts = append(parts, fmt.Sprintf("\"takeProfit\":\"%.8f\"", *tp))
	}
	if sl != nil {
		parts = append(parts, fmt.Sprintf("\"stopLoss\":\"%.8f\"", *sl))
	}
	body := "{" + strings.Join(parts, ",") + "}"
	_, err := c.signedPOST("/v5/position/trading-stop", body)
	return err
}

// ---------------- BingX client (Perp v2) ----------------

type bingxClient struct {
	apiKey    string
	apiSecret string
	hc        *http.Client
}

func newBingxClient(key, secret string) *bingxClient {
	return &bingxClient{apiKey: key, apiSecret: secret, hc: &http.Client{Timeout: 15 * time.Second}}
}

const bingxBase = "https://open-api.bingx.com"

func canonicalize(params map[string]string) string {
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	var parts []string
	for _, k := range keys {
		parts = append(parts, k+"="+params[k])
	}
	return strings.Join(parts, "&")
}

func canonicalizeEncoded(params map[string]string) string {
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	var parts []string
	for _, k := range keys {
		parts = append(parts, url.QueryEscape(k)+"="+url.QueryEscape(params[k]))
	}
	return strings.Join(parts, "&")
}

func (c *bingxClient) signedPOST(path string, params map[string]string) ([]byte, error) {
	if params == nil {
		params = make(map[string]string)
	}
	params["apiKey"] = c.apiKey
	ts := fmt.Sprintf("%d", time.Now().UnixMilli())
	params["timestamp"] = ts
	params["recvWindow"] = "5000"
	// Sign only canonical parameters (sorted, not URL-encoded)
	origin := canonicalize(params)
	mac := hmac.New(sha256.New, []byte(c.apiSecret))
	_, _ = mac.Write([]byte(origin))
	sign := hex.EncodeToString(mac.Sum(nil))
	// Send signature in body as 'signature'
	params["signature"] = sign
	bodyQS := canonicalizeEncoded(params)
	urlStr := bingxBase + path
	req, err := http.NewRequest(http.MethodPost, urlStr, strings.NewReader(bodyQS))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("X-BX-APIKEY", c.apiKey)
	// Timestamp usually not required as header when present in body
	res, err := c.hc.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	b, _ := io.ReadAll(res.Body)
	if res.StatusCode/100 != 2 {
		return nil, fmt.Errorf("bingx %s: %s", res.Status, string(b))
	}
	return b, nil
}

func (c *bingxClient) placeMarketOrder(symbol, side string, qty float64) (string, error) {
	params := map[string]string{
		"symbol":   symbol,
		"side":     strings.ToUpper(side),
		"type":     "MARKET",
		"quantity": fmt.Sprintf("%.8f", qty),
		"positionSide": func() string {
			if strings.EqualFold(side, "BUY") {
				return "LONG"
			}
			return "SHORT"
		}(),
	}
	b, err := c.signedPOST("/openApi/swap/v2/trade/order", params)
	if err != nil {
		return "", err
	}
	// Try to parse generic response
	var raw map[string]any
	if err := json.Unmarshal(b, &raw); err == nil {
		// Expect code==0 for success
		if code, ok := raw["code"].(float64); ok && code != 0 {
			msg := ""
			if m, ok2 := raw["msg"].(string); ok2 {
				msg = m
			}
			return "", fmt.Errorf("bingx error code %.0f: %s", code, msg)
		}
		if data, ok := raw["data"].(map[string]any); ok {
			if oid, ok2 := data["orderId"].(string); ok2 {
				return oid, nil
			}
			if oidf, ok2 := data["orderId"].(float64); ok2 {
				return fmt.Sprintf("%.0f", oidf), nil
			}
		}
	}
	return string(b), nil
}

func (c *bingxClient) placeConditionalMarket(symbol, side, positionSide, orderType string, qty, stopPrice float64) (string, error) {
	params := map[string]string{
		"symbol":       symbol,
		"side":         strings.ToUpper(side),
		"positionSide": strings.ToUpper(positionSide),
		"type":         strings.ToUpper(orderType),
		"quantity":     fmt.Sprintf("%.8f", qty),
		"stopPrice":    fmt.Sprintf("%.8f", stopPrice),
		"workingType":  "MARK_PRICE",
	}
	b, err := c.signedPOST("/openApi/swap/v2/trade/order", params)
	if err != nil {
		return "", err
	}
	var raw map[string]any
	if err := json.Unmarshal(b, &raw); err == nil {
		if code, ok := raw["code"].(float64); ok && code != 0 {
			msg := ""
			if m, ok2 := raw["msg"].(string); ok2 {
				msg = m
			}
			return "", fmt.Errorf("bingx error code %.0f: %s", code, msg)
		}
		if data, ok := raw["data"].(map[string]any); ok {
			if oid, ok2 := data["orderId"].(string); ok2 {
				return oid, nil
			}
			if oidf, ok2 := data["orderId"].(float64); ok2 {
				return fmt.Sprintf("%.0f", oidf), nil
			}
		}
	}
	return string(b), nil
}
