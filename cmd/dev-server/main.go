package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
)

// Mock data structures matching Upbit API
type Notice struct {
	ID            int    `json:"id"`
	Category      string `json:"category"`
	Title         string `json:"title"`
	ListedAt      string `json:"listed_at"`
	FirstListedAt string `json:"first_listed_at"`
}

type AnnouncementsResponse struct {
	Success bool `json:"success"`
	Data    struct {
		Notices     []Notice `json:"notices"`
		TotalPages  int      `json:"total_pages"`
		CurrentPage int      `json:"current_page"`
	} `json:"data"`
}

type Market struct {
	Market        string `json:"market"`
	KoreanName    string `json:"korean_name"`
	EnglishName   string `json:"english_name"`
	MarketWarning string `json:"market_warning"`
}

// Service Center listing structure
type ServiceCenterListing struct {
	Title  string `json:"title"`
	Symbol string `json:"symbol"`
	Date   string `json:"date"`
	Href   string `json:"href"`
}

// In-memory storage
var (
	notices           []Notice
	markets           []Market
	serviceListings   []ServiceCenterListing
	mu                sync.RWMutex
	nextID            = 1000
	nextServiceListID = 1
)

func init() {
	// Initialize with some sample data
	now := time.Now().Format(time.RFC3339)

	notices = []Notice{
		{
			ID:            1,
			Category:      "trade",
			Title:         "Market Support for BTC (Bitcoin) Trading",
			ListedAt:      now,
			FirstListedAt: now,
		},
		{
			ID:            2,
			Category:      "trade",
			Title:         "Market Support for ETH (Ethereum) Trading",
			ListedAt:      now,
			FirstListedAt: now,
		},
	}

	markets = []Market{
		{Market: "KRW-BTC", KoreanName: "비트코인", EnglishName: "Bitcoin", MarketWarning: "NONE"},
		{Market: "KRW-ETH", KoreanName: "이더리움", EnglishName: "Ethereum", MarketWarning: "NONE"},
		{Market: "BTC-ETH", KoreanName: "이더리움", EnglishName: "Ethereum", MarketWarning: "NONE"},
	}

	serviceListings = []ServiceCenterListing{
		{
			Title:  "Market Support for BTC (Bitcoin) Trading",
			Symbol: "BTC",
			Date:   "2024-08-24",
			Href:   "/service_center/notice/1001",
		},
		{
			Title:  "Market Support for ETH (Ethereum) Trading",
			Symbol: "ETH",
			Date:   "2024-08-24",
			Href:   "/service_center/notice/1002",
		},
	}

	nextID = 3
	nextServiceListID = 3
}

// Handler for announcements API (news)
func handleAnnouncements(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse query parameters
	pageStr := r.URL.Query().Get("page")
	perPageStr := r.URL.Query().Get("per_page")
	category := r.URL.Query().Get("category")

	page := 1
	perPage := 20

	if pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 {
			perPage = pp
		}
	}

	mu.RLock()
	defer mu.RUnlock()

	// Filter by category if specified
	filteredNotices := notices
	if category != "" {
		var filtered []Notice
		for _, notice := range notices {
			if notice.Category == category {
				filtered = append(filtered, notice)
			}
		}
		filteredNotices = filtered
	}

	// Calculate pagination
	totalNotices := len(filteredNotices)
	totalPages := (totalNotices + perPage - 1) / perPage

	start := (page - 1) * perPage
	end := start + perPage

	if start >= totalNotices {
		start = totalNotices
		end = totalNotices
	} else if end > totalNotices {
		end = totalNotices
	}

	pageNotices := filteredNotices[start:end]

	response := AnnouncementsResponse{
		Success: true,
	}
	response.Data.Notices = pageNotices
	response.Data.TotalPages = totalPages
	response.Data.CurrentPage = page

	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	json.NewEncoder(w).Encode(response)
}

// Handler for markets API
func handleMarkets(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	mu.RLock()
	defer mu.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	json.NewEncoder(w).Encode(markets)
}

// Admin handler to add new notice
func handleAddNotice(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		Category string `json:"category"`
		Title    string `json:"title"`
		Symbol   string `json:"symbol,omitempty"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	if req.Category == "" || req.Title == "" {
		http.Error(w, "Category and title are required", http.StatusBadRequest)
		return
	}

	mu.Lock()
	defer mu.Unlock()

	now := time.Now().Format(time.RFC3339)
	notice := Notice{
		ID:            nextID,
		Category:      req.Category,
		Title:         req.Title,
		ListedAt:      now,
		FirstListedAt: now,
	}

	// Insert at the beginning to simulate newest first
	notices = append([]Notice{notice}, notices...)
	nextID++

	log.Printf("Added notice: ID=%d, Category=%s, Title=%s", notice.ID, notice.Category, notice.Title)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"notice":  notice,
	})
}

// Admin handler to add new market
func handleAddMarket(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		Market      string `json:"market"`
		KoreanName  string `json:"korean_name"`
		EnglishName string `json:"english_name"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	if req.Market == "" || req.EnglishName == "" {
		http.Error(w, "Market and english_name are required", http.StatusBadRequest)
		return
	}

	mu.Lock()
	defer mu.Unlock()

	market := Market{
		Market:        req.Market,
		KoreanName:    req.KoreanName,
		EnglishName:   req.EnglishName,
		MarketWarning: "NONE",
	}

	markets = append(markets, market)

	log.Printf("Added market: %s (%s)", market.Market, market.EnglishName)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"market":  market,
	})
}

// Handler for service center HTML page
func handleServiceCenter(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	mu.RLock()
	defer mu.RUnlock()

	// Generate HTML page with service center listings
	html := `<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>업비트 고객센터 - 공지사항</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .notice-item { border: 1px solid #ddd; margin: 10px 0; padding: 15px; }
        .notice-title { font-weight: bold; color: #333; }
        .notice-date { color: #666; font-size: 0.9em; }
        a { text-decoration: none; color: inherit; }
        a:hover { background-color: #f5f5f5; }
    </style>
</head>
<body>
    <h1>업비트 고객센터 - 공지사항</h1>
    <div class="notices">`

	for _, listing := range serviceListings {
		html += fmt.Sprintf(`
        <div class="notice-item">
            <a href="%s">
                <div class="notice-title">%s</div>
                <div class="notice-date">%s</div>
            </a>
        </div>`, listing.Href, listing.Title, listing.Date)
	}

	html += `
    </div>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Write([]byte(html))
}

// Admin handler to add new service center listing
func handleAddServiceListing(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		Title  string `json:"title"`
		Symbol string `json:"symbol"`
		Date   string `json:"date,omitempty"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	if req.Title == "" || req.Symbol == "" {
		http.Error(w, "Title and symbol are required", http.StatusBadRequest)
		return
	}

	mu.Lock()
	defer mu.Unlock()

	if req.Date == "" {
		req.Date = time.Now().Format("2006-01-02")
	}

	listing := ServiceCenterListing{
		Title:  req.Title,
		Symbol: strings.ToUpper(req.Symbol),
		Date:   req.Date,
		Href:   fmt.Sprintf("/service_center/notice/%d", nextServiceListID+1000),
	}

	// Insert at the beginning to simulate newest first
	serviceListings = append([]ServiceCenterListing{listing}, serviceListings...)
	nextServiceListID++

	log.Printf("Added service center listing: Symbol=%s, Title=%s", listing.Symbol, listing.Title)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"listing": listing,
	})
}

// Admin handler to list all data
func handleAdmin(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	mu.RLock()
	defer mu.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"notices":          notices,
		"markets":          markets,
		"service_listings": serviceListings,
	})
}

func main() {
	port := ":8080"

	// Upbit API endpoints
	http.HandleFunc("/api/v1/announcements", handleAnnouncements)
	http.HandleFunc("/v1/market/all", handleMarkets)
	http.HandleFunc("/service_center/notice", handleServiceCenter)

	// Admin endpoints for adding data
	http.HandleFunc("/admin/add-notice", handleAddNotice)
	http.HandleFunc("/admin/add-market", handleAddMarket)
	http.HandleFunc("/admin/add-service-listing", handleAddServiceListing)
	http.HandleFunc("/admin/data", handleAdmin)

	// Health check
	http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})

	log.Printf("Starting Upbit API dev server on port %s", port)
	log.Printf("Announcements API: http://localhost%s/api/v1/announcements", port)
	log.Printf("Markets API: http://localhost%s/v1/market/all", port)
	log.Printf("Service Center: http://localhost%s/service_center/notice", port)
	log.Printf("Admin panel: http://localhost%s/admin/data", port)
	log.Printf("Add notice: POST http://localhost%s/admin/add-notice", port)
	log.Printf("Add market: POST http://localhost%s/admin/add-market", port)
	log.Printf("Add service listing: POST http://localhost%s/admin/add-service-listing", port)

	if err := http.ListenAndServe(port, nil); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}
