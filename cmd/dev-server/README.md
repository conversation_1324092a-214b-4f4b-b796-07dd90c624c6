# Upbit API Dev Server

Этот сервер эмулирует API Upbit для разработки и тестирования. Он предоставляет те же эндпоинты, что и реальный API Upbit, но с возможностью добавления собственных данных.

## Запуск

```bash
# Из корня проекта
go run ./cmd/dev-server/main.go

# Или скомпилировать и запустить
go build -o dev-server ./cmd/dev-server
./dev-server
```

Сервер запустится на порту 8080.

## API Эндпоинты

### Публичные API (эмулируют Upbit)

- `GET /api/v1/announcements` - Получить объявления (новости)
  - Параметры: `page`, `per_page`, `category`, `os`
  - Пример: `http://localhost:8080/api/v1/announcements?page=1&per_page=20&category=trade`

- `GET /v1/market/all` - Получить список всех рынков
  - Параметры: `isDetails`
  - Пример: `http://localhost:8080/v1/market/all?isDetails=false`

### Административные API (для управления данными)

- `GET /admin/data` - Посмотреть все данные
- `POST /admin/add-notice` - Добавить новое объявление
- `POST /admin/add-market` - Добавить новый рынок
- `GET /health` - Проверка здоровья сервера

## Примеры использования

### Добавить новое объявление о листинге

```bash
curl -X POST http://localhost:8080/admin/add-notice \
  -H "Content-Type: application/json" \
  -d '{
    "category": "trade",
    "title": "Market Support for DOGE (Dogecoin) Trading",
    "symbol": "DOGE"
  }'
```

### Добавить новый рынок

```bash
curl -X POST http://localhost:8080/admin/add-market \
  -H "Content-Type: application/json" \
  -d '{
    "market": "KRW-DOGE",
    "korean_name": "도지코인",
    "english_name": "Dogecoin"
  }'
```

### Получить объявления

```bash
curl "http://localhost:8080/api/v1/announcements?page=1&per_page=20&category=trade"
```

### Получить рынки

```bash
curl "http://localhost:8080/v1/market/all?isDetails=false"
```

## Конфигурация основного приложения

Чтобы основное приложение использовало dev-server, измените в `config.json`:

```json
{
  "settings": {
    "api": {
      "playground": "dev"
    }
  }
}
```

Для возврата к продакшн API:

```json
{
  "settings": {
    "api": {
      "playground": "prod"
    }
  }
}
```

## Структура данных

### Notice (Объявление)
```json
{
  "id": 1,
  "category": "trade",
  "title": "Market Support for BTC (Bitcoin) Trading",
  "listed_at": "2025-01-24T10:00:00Z",
  "first_listed_at": "2025-01-24T10:00:00Z"
}
```

### Market (Рынок)
```json
{
  "market": "KRW-BTC",
  "korean_name": "비트코인",
  "english_name": "Bitcoin",
  "market_warning": "NONE"
}
```

## Логи

Сервер логирует все добавления данных:
- Добавление объявлений
- Добавление рынков
- HTTP запросы

## Особенности

- Данные хранятся в памяти и сбрасываются при перезапуске
- Автоматическая генерация ID для новых объявлений
- Поддержка CORS для веб-разработки
- Совместимость с форматом данных Upbit API
