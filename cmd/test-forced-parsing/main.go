package main

import (
	"fmt"
	"log"
	"time"
)

// Копируем необходимые функции из main.go для демонстрации
func getNextTargetTime(now time.Time) time.Time {
	minute := now.Minute()
	targetMinute := ((minute / 10) + 1) * 10
	if targetMinute >= 60 {
		// Next hour
		return time.Date(now.Year(), now.Month(), now.Day(), now.Hour()+1, 0, 0, 0, now.Location())
	}
	return time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), targetMinute, 0, 0, now.Location())
}

func getCurrentTargetTime(now time.Time) time.Time {
	minute := now.Minute()
	if minute%10 == 0 {
		return time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), minute, 0, 0, now.Location())
	}
	return time.Time{}
}

func isWithinForcedParsingWindow(now time.Time, beforeMs, afterMs int) bool {
	// Check current target time
	currentTarget := getCurrentTargetTime(now)
	if !currentTarget.IsZero() {
		// We're at a target minute, check if we're within the window
		diffMs := int(now.Sub(currentTarget).Milliseconds())
		return diffMs >= 0 && diffMs <= afterMs
	}
	
	// Check if we're before the next target time
	nextTarget := getNextTargetTime(now)
	diffMs := int(nextTarget.Sub(now).Milliseconds())
	return diffMs <= beforeMs
}

func main() {
	fmt.Println("=== Тест форсированного парсинга ===")
	fmt.Println()

	// Настройки для тестирования
	beforeMs := 30000 // 30 секунд до
	afterMs := 30000  // 30 секунд после

	fmt.Printf("Настройки: before=%dms, after=%dms\n", beforeMs, afterMs)
	fmt.Println("Целевые времена: xx:00, xx:10, xx:20, xx:30, xx:40, xx:50")
	fmt.Println()

	// Тестовые времена
	testTimes := []time.Time{
		time.Date(2024, 1, 1, 20, 9, 30, 0, time.UTC),  // 30s before 20:10
		time.Date(2024, 1, 1, 20, 9, 45, 0, time.UTC),  // 15s before 20:10
		time.Date(2024, 1, 1, 20, 10, 0, 0, time.UTC),  // exactly 20:10
		time.Date(2024, 1, 1, 20, 10, 15, 0, time.UTC), // 15s after 20:10
		time.Date(2024, 1, 1, 20, 10, 30, 0, time.UTC), // 30s after 20:10
		time.Date(2024, 1, 1, 20, 10, 45, 0, time.UTC), // 45s after 20:10 (should be false)
		time.Date(2024, 1, 1, 20, 15, 0, 0, time.UTC),  // 20:15 (not target time)
		time.Date(2024, 1, 1, 20, 19, 30, 0, time.UTC), // 30s before 20:20
		time.Date(2024, 1, 1, 20, 20, 0, 0, time.UTC),  // exactly 20:20
		time.Date(2024, 1, 1, 19, 59, 30, 0, time.UTC), // 30s before 20:00
		time.Date(2024, 1, 1, 20, 0, 0, 0, time.UTC),   // exactly 20:00
		time.Date(2024, 1, 1, 20, 0, 30, 0, time.UTC),  // 30s after 20:00
	}

	fmt.Println("Результаты тестирования:")
	fmt.Println("Время\t\t\tВ окне?\tТекущая цель\t\tСледующая цель")
	fmt.Println("-------------------------------------------------------------------")

	for _, testTime := range testTimes {
		inWindow := isWithinForcedParsingWindow(testTime, beforeMs, afterMs)
		currentTarget := getCurrentTargetTime(testTime)
		nextTarget := getNextTargetTime(testTime)

		currentStr := "нет"
		if !currentTarget.IsZero() {
			currentStr = currentTarget.Format("15:04")
		}

		fmt.Printf("%s\t%v\t%s\t\t\t%s\n",
			testTime.Format("15:04:05"),
			inWindow,
			currentStr,
			nextTarget.Format("15:04"),
		)
	}

	fmt.Println()
	fmt.Println("=== Симуляция работы в реальном времени ===")
	fmt.Println("Нажмите Ctrl+C для остановки")
	fmt.Println()

	// Симуляция работы воркера
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	var lastState bool
	requestCount := 0

	for {
		select {
		case <-ticker.C:
			now := time.Now()
			inWindow := isWithinForcedParsingWindow(now, beforeMs, afterMs)

			if inWindow != lastState {
				if inWindow {
					log.Printf("🚀 НАЧАЛО форсированного парсинга в %s", now.Format("15:04:05"))
					requestCount = 0
				} else {
					log.Printf("⏹️  КОНЕЦ форсированного парсинга в %s (выполнено %d запросов)", now.Format("15:04:05"), requestCount)
				}
				lastState = inWindow
			}

			if inWindow {
				requestCount++
				log.Printf("📡 Дополнительный запрос #%d в %s", requestCount, now.Format("15:04:05"))
			} else {
				nextTarget := getNextTargetTime(now)
				timeToNext := nextTarget.Sub(now)
				log.Printf("⏰ Ожидание до %s (осталось %v)", nextTarget.Format("15:04"), timeToNext.Round(time.Second))
			}
		}
	}
}
