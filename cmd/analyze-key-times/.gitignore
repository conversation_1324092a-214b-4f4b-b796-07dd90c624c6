*.DS_Store
.AppleDouble
.LSOverride
._*

Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
Desktop.ini
$RECYCLE.BIN/

.directory
.dropbox
.dropbox.attr

*.log
*.log.*
*.sql
*.sqlite
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar
*.exe
*.dll
*.so
*.dylib
*.bak
*.swp
*~
*.tmp

__pycache__/
*.py[cod]
*$py.class
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/
.coverage
.coverage.*
coverage.xml
*.cover

bin/
pkg/
*.test
*.prof

## Rust
target/
*.rs.bk

build/
*.o
*.obj
*.out
*.a
*.lib
*.pdb

target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
dependency-reduced-pom.xml
release.properties
tomcat*/
*.class

bin/
obj/
*.user
*.suo
*.csproj.bak
*.cache
*.ilk
*.meta
*.ncx
*.nupkg

_build/
deps/
*.ez

.Rhistory
.RData
.Rproj.user/
*.Rout

node_modules/
dist/
build/
*.min.*
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.tsbuildinfo

DerivedData/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
*.xccheckout
*.moved-aside
*.xcuserstate
*.xcworkspace
Pods/

.gradle/
build/
*.apk
*.ap_
*.aab
local.properties
*.idea/
*.iml

.idea/
*.iml
.vscode/
*.swp
*.swo
nbproject/
*.code-workspace

.env
.env.local
.env.*.local
*.cache
*.lock
*.pid
