package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
)

// ListingItem представляет элемент из JSON файла
type ListingItem struct {
	DateUTC                string    `json:"date_utc"`
	DateMSK                string    `json:"date_msk"`
	DateKST                string    `json:"date_kst"`
	Category               string    `json:"category"`
	ListedAt               string    `json:"listed_at"`
	FirstListedAt          string    `json:"first_listed_at"`
	ListedAtTimestamp      int64     `json:"listed_at_timestamp"`
	FirstListedAtTimestamp int64     `json:"first_listed_at_timestamp"`
	ID                     int       `json:"id"`
	Href                   string    `json:"href"`
	Symbol                 string    `json:"symbol"`
	Title                  string    `json:"title"`
	Listing                bool      `json:"listing"`
	SavedAtTimestamp       int64     `json:"saved_at_timestamp"`
	Exchanges              []string  `json:"exchanges,omitempty"`
	MinuteReturnsListedAt  []float64 `json:"minute_returns_listed_at,omitempty"`
}

// ListingData представляет структуру JSON файла
type ListingData struct {
	Items []ListingItem `json:"items"`
}

// KeyTimeAnalysis представляет анализ по ключевому времени
type KeyTimeAnalysis struct {
	Time         string
	ExactMatches int
	Within5Min   int
	Within10Min  int
	Within15Min  int
	ListingTrue  int
	ListingFalse int
	Categories   map[string]int
	Symbols      []string
	Dates        []string
	Examples     []string // Примеры времени

	// Отдельные списки символов для каждого интервала
	ExactMatchSymbols  []string
	Within5MinSymbols  []string
	Within10MinSymbols []string
	Within15MinSymbols []string
}

func main() {
	if len(os.Args) != 2 {
		fmt.Println("Использование: go run main.go <путь_к_listing.json>")
		fmt.Println("Пример: go run main.go ../../listing.json")
		os.Exit(1)
	}

	jsonFile := os.Args[1]

	// Читаем JSON файл
	data, err := os.ReadFile(jsonFile)
	if err != nil {
		log.Fatalf("Ошибка чтения файла: %v", err)
	}

	// Парсим JSON
	var listingData ListingData
	if err := json.Unmarshal(data, &listingData); err != nil {
		log.Fatalf("Ошибка парсинга JSON: %v", err)
	}

	fmt.Printf("🎯 АНАЛИЗ КЛЮЧЕВЫХ ВРЕМЕННЫХ ПАТТЕРНОВ ЛИСТИНГА\n")
	fmt.Printf("📊 Файл: %s\n", jsonFile)
	fmt.Printf("📈 Всего элементов: %d\n\n", len(listingData.Items))

	// Ключевые времена для анализа
	keyTimes := []string{
		"16:30", // ГЛАВНОЕ время листинга
		"18:00", // второе по частоте время
		"12:30", // третье по частоте время
		"11:19", // утренние листинги
		"13:00", // дневные листинги
		"00:57", // редкие ночные листинги
	}

	// Анализируем каждое ключевое время
	analysis := analyzeKeyTimes(listingData.Items, keyTimes)

	// Выводим результаты
	printKeyTimeAnalysis(analysis)

	// Сохраняем детальный отчет
	saveKeyTimeReport(analysis)
}

func analyzeKeyTimes(items []ListingItem, keyTimes []string) map[string]*KeyTimeAnalysis {
	analysis := make(map[string]*KeyTimeAnalysis)

	// Инициализируем анализ для каждого ключевого времени
	for _, keyTime := range keyTimes {
		analysis[keyTime] = &KeyTimeAnalysis{
			Time:       keyTime,
			Categories: make(map[string]int),
			Symbols:    []string{},
			Dates:      []string{},
			Examples:   []string{},

			// Инициализируем списки символов для каждого интервала
			ExactMatchSymbols:  []string{},
			Within5MinSymbols:  []string{},
			Within10MinSymbols: []string{},
			Within15MinSymbols: []string{},
		}
	}

	for _, item := range items {
		// Извлекаем точное время
		exactTime := extractExactTime(item.ListedAt)
		if exactTime == "" {
			continue
		}

		// Проверяем каждое ключевое время
		for _, keyTime := range keyTimes {
			keyAnalysis := analysis[keyTime]

			// Проверяем точное совпадение
			if isExactTimeMatch(exactTime, keyTime) {
				keyAnalysis.ExactMatches++

				if item.Listing {
					keyAnalysis.ListingTrue++
				} else {
					keyAnalysis.ListingFalse++
				}

				// Категории
				keyAnalysis.Categories[item.Category]++

				// Символы для точного совпадения
				if item.Symbol != "" {
					keyAnalysis.Symbols = append(keyAnalysis.Symbols, item.Symbol)
					keyAnalysis.ExactMatchSymbols = append(keyAnalysis.ExactMatchSymbols, item.Symbol)
				}

				// Даты
				date := extractDate(item.ListedAt)
				if date != "" {
					keyAnalysis.Dates = append(keyAnalysis.Dates, date)
				}

				// Примеры времени
				keyAnalysis.Examples = append(keyAnalysis.Examples, exactTime)
			}

			// Проверяем в пределах 5, 10, 15 минут
			minutesDiff := getMinutesDifference(exactTime, keyTime)
			if minutesDiff <= 5 {
				keyAnalysis.Within5Min++
				// Добавляем символ в список для 5 минут
				if item.Symbol != "" {
					keyAnalysis.Within5MinSymbols = append(keyAnalysis.Within5MinSymbols, item.Symbol)
				}
			}
			if minutesDiff <= 10 {
				keyAnalysis.Within10Min++
				// Добавляем символ в список для 10 минут
				if item.Symbol != "" {
					keyAnalysis.Within10MinSymbols = append(keyAnalysis.Within10MinSymbols, item.Symbol)
				}
			}
			if minutesDiff <= 15 {
				keyAnalysis.Within15Min++
				// Добавляем символ в список для 15 минут
				if item.Symbol != "" {
					keyAnalysis.Within15MinSymbols = append(keyAnalysis.Within15MinSymbols, item.Symbol)
				}
			}
		}
	}

	return analysis
}

func extractExactTime(listedAt string) string {
	if listedAt == "" {
		return ""
	}

	// Парсим ISO 8601 формат
	t, err := time.Parse("2006-01-02T15:04:05-07:00", listedAt)
	if err != nil {
		// Пробуем без timezone
		t, err = time.Parse("2006-01-02T15:04:05", listedAt)
		if err != nil {
			return ""
		}
	}

	return t.Format("15:04:05")
}

func extractDate(listedAt string) string {
	if listedAt == "" {
		return ""
	}

	// Парсим ISO 8601 формат
	t, err := time.Parse("2006-01-02T15:04:05-07:00", listedAt)
	if err != nil {
		// Пробуем без timezone
		t, err = time.Parse("2006-01-02T15:04:05", listedAt)
		if err != nil {
			return ""
		}
	}

	return t.Format("2006-01-02")
}

func isExactTimeMatch(exactTime, keyTime string) bool {
	// Извлекаем часы и минуты из exactTime (формат "15:04:05")
	parts := strings.Split(exactTime, ":")
	if len(parts) < 2 {
		return false
	}

	exactHM := parts[0] + ":" + parts[1]
	return exactHM == keyTime
}

func getMinutesDifference(exactTime, keyTime string) int {
	// Парсим ключевое время
	keyParts := strings.Split(keyTime, ":")
	if len(keyParts) < 2 {
		return 999
	}

	keyHour, _ := strconv.Atoi(keyParts[0])
	keyMin, _ := strconv.Atoi(keyParts[1])

	// Парсим точное время
	exactParts := strings.Split(exactTime, ":")
	if len(exactParts) < 2 {
		return 999
	}

	exactHour, _ := strconv.Atoi(exactParts[0])
	exactMin, _ := strconv.Atoi(exactParts[1])

	// Вычисляем разницу в минутах
	keyTotalMin := keyHour*60 + keyMin
	exactTotalMin := exactHour*60 + exactMin

	diff := abs(exactTotalMin - keyTotalMin)

	// Учитываем переход через полночь
	if diff > 720 { // 12 часов = 720 минут
		diff = 1440 - diff // 24 часа = 1440 минут
	}

	return diff
}

func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func printKeyTimeAnalysis(analysis map[string]*KeyTimeAnalysis) {
	fmt.Println("🕐 ДЕТАЛЬНЫЙ АНАЛИЗ КЛЮЧЕВЫХ ВРЕМЕННЫХ ПАТТЕРНОВ")
	fmt.Println(strings.Repeat("=", 80))

	// Сортируем по количеству точных совпадений
	var keyTimes []string
	for keyTime := range analysis {
		keyTimes = append(keyTimes, keyTime)
	}

	sort.Slice(keyTimes, func(i, j int) bool {
		return analysis[keyTimes[i]].ExactMatches > analysis[keyTimes[j]].ExactMatches
	})

	for _, keyTime := range keyTimes {
		keyAnalysis := analysis[keyTime]

		fmt.Printf("\n🎯 %s - КЛЮЧЕВОЕ ВРЕМЯ ЛИСТИНГА\n", keyTime)
		fmt.Println(strings.Repeat("-", 60))

		// Основная статистика
		fmt.Printf("📊 Точных совпадений: %d\n", keyAnalysis.ExactMatches)
		fmt.Printf("⏰ В пределах 5 минут: %d\n", keyAnalysis.Within5Min)
		fmt.Printf("⏰ В пределах 10 минут: %d\n", keyAnalysis.Within10Min)
		fmt.Printf("⏰ В пределах 15 минут: %d\n", keyAnalysis.Within15Min)

		if keyAnalysis.ExactMatches > 0 {
			fmt.Printf("✅ Успешных листингов: %d\n", keyAnalysis.ListingTrue)
			fmt.Printf("❌ Не листингов: %d\n", keyAnalysis.ListingFalse)

			// Категории
			if len(keyAnalysis.Categories) > 0 {
				fmt.Printf("📂 Категории: ")
				var cats []string
				for cat, count := range keyAnalysis.Categories {
					cats = append(cats, fmt.Sprintf("%s(%d)", cat, count))
				}
				sort.Strings(cats)
				fmt.Printf("%s\n", strings.Join(cats, ", "))
			}

			// Символы для точных совпадений
			if len(keyAnalysis.ExactMatchSymbols) > 0 {
				fmt.Printf("💎 Символы (точные совпадения): %s\n", strings.Join(keyAnalysis.ExactMatchSymbols, ", "))
			}

			// Символы в пределах 5 минут
			if len(keyAnalysis.Within5MinSymbols) > 0 {
				fmt.Printf("💎 Символы (в пределах 5 мин): %s\n", strings.Join(keyAnalysis.Within5MinSymbols, ", "))
			}

			// Символы в пределах 10 минут
			if len(keyAnalysis.Within10MinSymbols) > 0 {
				fmt.Printf("💎 Символы (в пределах 10 мин): %s\n", strings.Join(keyAnalysis.Within10MinSymbols, ", "))
			}

			// Символы в пределах 15 минут
			if len(keyAnalysis.Within15MinSymbols) > 0 {
				fmt.Printf("💎 Символы (в пределах 15 мин): %s\n", strings.Join(keyAnalysis.Within15MinSymbols, ", "))
			}

			// Примеры времени
			if len(keyAnalysis.Examples) > 0 {
				fmt.Printf("🕐 Примеры времени: %s\n", strings.Join(keyAnalysis.Examples, ", "))
			}
		} else {
			fmt.Println("⚠️  Нет точных совпадений")
		}
	}

	fmt.Println("\n" + strings.Repeat("=", 80))
}

func saveKeyTimeReport(analysis map[string]*KeyTimeAnalysis) {
	// Создаем детальный отчет
	report := fmt.Sprintf(`# Анализ ключевых временных паттернов листинга Upbit

## Ключевые времена листинга

%s

## Общая статистика

| Время | Точных совпадений | В пределах 5 мин | В пределах 10 мин | В пределах 15 мин | ✅ Listing | ❌ Not Listing |
|-------|-------------------|------------------|-------------------|-------------------|------------|----------------|
`,
		formatKeyTimeReport(analysis))

	// Добавляем таблицу
	var keyTimes []string
	for keyTime := range analysis {
		keyTimes = append(keyTimes, keyTime)
	}

	sort.Slice(keyTimes, func(i, j int) bool {
		return analysis[keyTimes[i]].ExactMatches > analysis[keyTimes[j]].ExactMatches
	})

	for _, keyTime := range keyTimes {
		keyAnalysis := analysis[keyTime]
		report += fmt.Sprintf("| %s | %d | %d | %d | %d | %d | %d |\n",
			keyTime,
			keyAnalysis.ExactMatches,
			keyAnalysis.Within5Min,
			keyAnalysis.Within10Min,
			keyAnalysis.Within15Min,
			keyAnalysis.ListingTrue,
			keyAnalysis.ListingFalse)
	}

	// Сохраняем в файл
	err := os.WriteFile("key_times_analysis.md", []byte(report), 0644)
	if err != nil {
		log.Printf("Ошибка сохранения отчета: %v", err)
	} else {
		fmt.Println("📄 Детальный отчет по ключевым временам сохранен в: key_times_analysis.md")
	}
}

func formatKeyTimeReport(analysis map[string]*KeyTimeAnalysis) string {
	var result strings.Builder

	var keyTimes []string
	for keyTime := range analysis {
		keyTimes = append(keyTimes, keyTime)
	}

	sort.Slice(keyTimes, func(i, j int) bool {
		return analysis[keyTimes[i]].ExactMatches > analysis[keyTimes[j]].ExactMatches
	})

	for _, keyTime := range keyTimes {
		keyAnalysis := analysis[keyTime]

		result.WriteString(fmt.Sprintf("### %s\n\n", keyTime))

		if keyAnalysis.ExactMatches > 0 {
			result.WriteString(fmt.Sprintf("- **Точных совпадений**: %d\n", keyAnalysis.ExactMatches))
			result.WriteString(fmt.Sprintf("- **В пределах 5 минут**: %d\n", keyAnalysis.Within5Min))
			result.WriteString(fmt.Sprintf("- **В пределах 10 минут**: %d\n", keyAnalysis.Within10Min))
			result.WriteString(fmt.Sprintf("- **В пределах 15 минут**: %d\n", keyAnalysis.Within15Min))
			result.WriteString(fmt.Sprintf("- **Успешных листингов**: %d\n", keyAnalysis.ListingTrue))
			result.WriteString(fmt.Sprintf("- **Не листингов**: %d\n", keyAnalysis.ListingFalse))

			if len(keyAnalysis.Categories) > 0 {
				result.WriteString("- **Категории**: ")
				var cats []string
				for cat, count := range keyAnalysis.Categories {
					cats = append(cats, fmt.Sprintf("%s(%d)", cat, count))
				}
				sort.Strings(cats)
				result.WriteString(strings.Join(cats, ", ") + "\n")
			}

			// Символы для точных совпадений
			if len(keyAnalysis.ExactMatchSymbols) > 0 {
				result.WriteString(fmt.Sprintf("- **Символы (точные совпадения)**: %s\n", strings.Join(keyAnalysis.ExactMatchSymbols, ", ")))
			}

			// Символы в пределах 5 минут
			if len(keyAnalysis.Within5MinSymbols) > 0 {
				result.WriteString(fmt.Sprintf("- **Символы (в пределах 5 мин)**: %s\n", strings.Join(keyAnalysis.Within5MinSymbols, ", ")))
			}

			// Символы в пределах 10 минут
			if len(keyAnalysis.Within10MinSymbols) > 0 {
				result.WriteString(fmt.Sprintf("- **Символы (в пределах 10 мин)**: %s\n", strings.Join(keyAnalysis.Within10MinSymbols, ", ")))
			}

			// Символы в пределах 15 минут
			if len(keyAnalysis.Within15MinSymbols) > 0 {
				result.WriteString(fmt.Sprintf("- **Символы (в пределах 15 мин)**: %s\n", strings.Join(keyAnalysis.Within15MinSymbols, ", ")))
			}
		} else {
			result.WriteString("- Нет точных совпадений\n")
		}

		result.WriteString("\n")
	}

	return result.String()
}
