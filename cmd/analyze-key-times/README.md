# Анализатор ключевых временных паттернов листинга Upbit

## Описание
Специализированный Go скрипт для анализа ключевых временных паттернов листинга криптовалют на бирже Upbit с точностью до 5 минут.

## 🎯 Ключевые времена для анализа

### Основные временные окна:
- **16:30 KST** - ГЛАВНОЕ время листинга (наиболее частое)
- **18:00 KST** - второе по частоте время
- **12:30 KST** - третье по частоте время
- **11:19 KST** - утренние листинги
- **13:00 KST** - дневные листинги
- **00:57 KST** - редкие ночные листинги

## 📊 Анализируемые параметры

### Точность совпадений:
- **Точные совпадения** - время точно совпадает (до минут)
- **В пределах 5 минут** - разница ≤ 5 минут
- **В пределах 10 минут** - разница ≤ 10 минут
- **В пределах 15 минут** - разница ≤ 15 минут

### Дополнительная статистика:
- Количество успешных листингов (`listing: true`)
- Количество уведомлений (`listing: false`)
- Распределение по категориям
- Список символов криптовалют
- Примеры точного времени
- Даты листингов

## 🚀 Установка и запуск

### Требования:
- Go 1.19+

### Запуск:
```bash
# Переход в директорию
cd cmd/analyze-key-times

# Запуск скрипта
go run main.go <путь_к_listing.json>

# Пример:
go run main.go ../../listing.json
```

### Компиляция:
```bash
# Сборка исполняемого файла
go build -o analyze-key-times main.go

# Запуск скомпилированного файла
./analyze-key-times ../../listing.json
```

## 📈 Примеры вывода

### Консольный вывод:
```
🎯 16:30 - КЛЮЧЕВОЕ ВРЕМЯ ЛИСТИНГА
------------------------------------------------------------
📊 Точных совпадений: 9
⏰ В пределах 5 минут: 15
⏰ В пределах 10 минут: 23
⏰ В пределах 15 минут: 30
✅ Успешных листингов: 2
❌ Не листингов: 7
📂 Категории: Trade(9)
💎 Символы: CYBER, ASTR, RLY, PROM, OBSR, BCHA, SPND, 18종, API3
🕐 Примеры времени: 16:30:00, 16:30:01, 16:30:02, 16:30:30, 16:30:32, 16:30:36, 16:30:07, 16:30:00
```

### Markdown отчет:
- Детальная статистика по каждому ключевому времени
- Таблица с общими результатами
- Примеры и детали по каждому паттерну

## 🔍 Алгоритм анализа

### 1. Извлечение времени:
- Парсинг ISO 8601 формата (`2025-08-12T16:30:00+09:00`)
- Извлечение точного времени (`16:30:00`)

### 2. Проверка совпадений:
- **Точное совпадение**: `16:30:00` = `16:30`
- **В пределах 5 минут**: `16:25:00` - `16:35:00`
- **В пределах 10 минут**: `16:20:00` - `16:40:00`
- **В пределах 15 минут**: `16:15:00` - `16:45:00`

### 3. Учет перехода через полночь:
- Корректная обработка времен `00:57` и `23:45`
- Вычисление минимальной разницы в минутах

## 📊 Результаты анализа

### Статистика по ключевым временам:

| Время | Точных совпадений | В пределах 5 мин | В пределах 10 мин | В пределах 15 мин | ✅ Listing | ❌ Not Listing |
|-------|-------------------|------------------|-------------------|-------------------|------------|----------------|
| 18:00 | 19 | 29 | 36 | 47 | 0 | 19 |
| 16:30 | 9 | 15 | 23 | 30 | 2 | 7 |
| 13:00 | 6 | 8 | 8 | 10 | 0 | 6 |
| 12:30 | 3 | 5 | 5 | 7 | 2 | 1 |
| 00:57 | 2 | 4 | 4 | 4 | 1 | 1 |
| 11:19 | 2 | 6 | 11 | 16 | 2 | 0 |

## 🎯 Ключевые выводы

### 1. **18:00 KST** - Самое активное время:
- 19 точных совпадений
- 29 событий в пределах 5 минут
- Все события - уведомления (не листинги)

### 2. **16:30 KST** - Основное время листинга:
- 9 точных совпадений
- 2 успешных листингов
- Регулярные листинги каждую неделю

### 3. **12:30 KST** - Полуденное время:
- 3 точных совпадения
- 2 успешных листингов
- Стабильный паттерн

### 4. **11:19 KST** - Утренние листинги:
- 2 точных совпадения
- 100% успешных листингов
- Уникальный временной паттерн

## 🔧 Настройка и кастомизация

### Добавление новых ключевых времен:
```go
keyTimes := []string{
    "16:30", "18:00", "12:30", "11:19", "13:00", "00:57",
    "14:00", "15:00", // Добавьте новые времена
}
```

### Изменение временных интервалов:
```go
// В функции getMinutesDifference
if minutesDiff <= 5 {   // 5 минут
    keyAnalysis.Within5Min++
}
if minutesDiff <= 10 {  // 10 минут
    keyAnalysis.Within10Min++
}
if minutesDiff <= 15 {  // 15 минут
    keyAnalysis.Within15Min++
}
```

## 📁 Выходные файлы

### 1. `key_times_analysis.md`
- Детальный отчет в формате Markdown
- Статистика по каждому ключевому времени
- Общая таблица результатов

### 2. Консольный вывод
- Цветной интерфейс с эмодзи
- Сортировка по активности
- Детальная информация по каждому паттерну

## 🚀 Производительность

- Обрабатывает файлы размером 50+ MB
- Оптимизированная работа с памятью
- Быстрый анализ временных паттернов
- Эффективная группировка данных

## 🔮 Возможности расширения

- Экспорт в CSV/JSON форматы
- Интеграция с TUI мониторингом
- Алерты при отклонениях от паттернов
- Анализ трендов по времени
- Сравнение с другими биржами

## 📝 Лицензия
Проект является частью upbit-listing и следует тем же условиям лицензирования.
