package main

import (
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"io"
	"log"
	"math"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"time"
)

type cacheItem struct {
	DateUTC                    string    `json:"date_utc,omitempty"`
	DateMSK                    string    `json:"date_msk,omitempty"`
	DateKST                    string    `json:"date_kst,omitempty"`
	DateLegacy                 string    `json:"date,omitempty"`
	Category                   string    `json:"category,omitempty"`
	ListedAt                   string    `json:"listed_at,omitempty"`
	FirstListedAt              string    `json:"first_listed_at,omitempty"`
	ListedAtTimestamp          int64     `json:"listed_at_timestamp,omitempty"`
	FirstListedAtTimestamp     int64     `json:"first_listed_at_timestamp,omitempty"`
	ID                         int       `json:"id,omitempty"`
	Href                       string    `json:"href"`
	Symbol                     string    `json:"symbol"`
	Title                      string    `json:"title"`
	Listing                    bool      `json:"listing"`
	SavedAtTimestamp           int64     `json:"saved_at_timestamp,omitempty"`
	Timestamp                  int64     `json:"-"`
	Exchanges                  []string  `json:"exchanges,omitempty"`
	MinuteReturnsListedAt      []float64 `json:"minute_returns_listed_at,omitempty"`
	MinuteReturnsFirstListedAt []float64 `json:"minute_returns_first_listed_at,omitempty"`
}

type listingDocument struct {
	Items          []cacheItem `json:"items"`
	LatestID       int         `json:"latest_id,omitempty"`
	LatestHref     string      `json:"latest_href,omitempty"`
	LatestListedAt string      `json:"latest_listed_at,omitempty"`
	SavedAt        string      `json:"saved_at,omitempty"`
}

type Candle struct {
	T               int64   `json:"t"` // open time ms
	O               float64 `json:"o"`
	H               float64 `json:"h"`
	L               float64 `json:"l"`
	C               float64 `json:"c"`
	V               float64 `json:"v"`
	ProfitPercent0m float64 `json:"profit_percent_0m,omitempty"`
	ProfitPercent1m float64 `json:"profit_percent_1m,omitempty"`
	ProfitPercent2m float64 `json:"profit_percent_2m,omitempty"`
	ProfitPercent3m float64 `json:"profit_percent_3m,omitempty"`
}

type CandleBundle struct {
	Symbol         string   `json:"symbol"`
	Exchange       string   `json:"exchange"`
	Interval       string   `json:"interval"`
	StartTimestamp int64    `json:"start_timestamp"`
	Candles        []Candle `json:"candles"`
}

func main() {
	printStartupBanner("generate-candles")
	flag.Parse()
	cwd, _ := os.Getwd()
	listPath := filepath.Join(cwd, "listing.json")
	candlesDir := filepath.Join(cwd, "candles")
	_ = os.MkdirAll(candlesDir, 0o755)

	items, _ := readCache(listPath)
	log.Printf("loaded %d items", len(items))
	client := &http.Client{Timeout: 20 * time.Second}

	// Build exchange index like recheck-exchanges
	exIndex := buildExchangeIndex(client)

	// Update exchanges on items if missing
	updatedEx := 0
	for i := range items {
		if !items[i].Listing || items[i].Symbol == "" {
			continue
		}
		base := strings.ToUpper(strings.TrimSpace(items[i].Symbol))
		if ex := exIndex[base]; len(ex) > 0 {
			merged := mergeUniqueSorted(items[i].Exchanges, ex)
			if !equalStringSlices(merged, items[i].Exchanges) {
				items[i].Exchanges = merged
				updatedEx++
			}
		}
	}
	if updatedEx > 0 {
		_ = writeCache(listPath, items)
		log.Printf("updated exchanges for %d items", updatedEx)
	}

	processed := 0
	for i := range items {
		if !items[i].Listing {
			continue
		}
		base := resolveBaseSymbol(items[i])
		if base == "" {
			continue
		}
		starts := collectStartTimestamps(items[i])
		if len(starts) == 0 {
			continue
		}
		// Preferred exchanges per order but filtered by availability
		order := []string{"bybit", "bingx", "binance", "mexc", "bitget"}
		avail := map[string]bool{}
		for _, e := range exIndex[base] {
			avail[strings.ToLower(e)] = true
		}

		for idx, start := range starts {
			candles, usedEx := fetchCandlesAny(client, base, start, order, avail)
			if len(candles) == 0 {
				log.Printf("%s: no candles found from %d across exchanges", base, start)
				continue
			}
			// Annotate profit percent relative to first high
			candles = addProfitPercent(candles)
			// Save candles
			bundle := CandleBundle{Symbol: base + "USDT", Exchange: usedEx, Interval: "1m", StartTimestamp: start, Candles: candles}
			saveCandles(candlesDir, base, bundle)
			// Compute returns
			rets := computeMinuteReturnsHighToLow(candles)
			if idx == 0 {
				items[i].MinuteReturnsListedAt = rets
			} else if idx == 1 {
				// if second start equals first, we will overwrite with same values which is fine
				items[i].MinuteReturnsFirstListedAt = rets
			}
		}
		processed++
		if processed%10 == 0 {
			_ = writeCache(listPath, items)
		}
	}
	_ = writeCache(listPath, items)
	fmt.Printf("Processed %d listing items.\n", processed)
}

func printStartupBanner(name string) {
	fmt.Println("# " + name)
	fmt.Println("Version: 0.0.1")
	fmt.Println("Build: " + buildDate)
	fmt.Println("Commit: " + commitHash)
	fmt.Println("Author: https://t.me/suenot")
	fmt.Println("---")
	flag.PrintDefaults()
}

var buildDate = "unknown"
var commitHash = "unknown"

var symbolParenRegex = regexp.MustCompile(`\(([A-Z0-9\-]{2,})\)`)

func resolveBaseSymbol(it cacheItem) string {
	base := strings.ToUpper(strings.TrimSpace(it.Symbol))
	if base == "" || base == "USDT" || base == "BTC" || base == "KRW" {
		// try to extract from title
		if m := symbolParenRegex.FindStringSubmatch(it.Title); len(m) == 2 {
			base = strings.ToUpper(strings.TrimSpace(m[1]))
		}
	}
	// filter obviously wrong again
	if base == "" || base == "USDT" || base == "BTC" || base == "KRW" {
		return ""
	}
	return base
}

func collectStartTimestamps(it cacheItem) []int64 {
	seen := map[int64]struct{}{}
	out := []int64{}
	if it.ListedAtTimestamp > 0 {
		seen[it.ListedAtTimestamp] = struct{}{}
		out = append(out, it.ListedAtTimestamp)
	}
	if it.FirstListedAtTimestamp > 0 {
		if _, ok := seen[it.FirstListedAtTimestamp]; !ok {
			out = append(out, it.FirstListedAtTimestamp)
		}
	}
	return out
}

func computeMinuteReturnsHighToLow(candles []Candle) []float64 {
	if len(candles) == 0 {
		return nil
	}
	firstHigh := candles[0].H
	if firstHigh <= 0 || math.IsNaN(firstHigh) || math.IsInf(firstHigh, 0) {
		return nil
	}
	max := 120
	if len(candles) < max {
		max = len(candles)
	}
	rets := make([]float64, 0, max)
	for i := 0; i < max; i++ {
		v := (candles[i].L - firstHigh) / firstHigh * 100
		rets = append(rets, v)
	}
	return rets
}

func saveCandles(dir string, base string, bundle CandleBundle) {
	// Build filename by KST date of start timestamp: YYYY-MM-DD-<SYMBOL>.json
	kst, _ := time.LoadLocation("Asia/Seoul")
	date := time.UnixMilli(bundle.StartTimestamp).In(kst).Format("2006-01-02")
	filename := fmt.Sprintf("%s-%s.json", date, base)
	path := filepath.Join(dir, filename)

	var existing []CandleBundle
	if b, err := os.ReadFile(path); err == nil && len(b) > 0 {
		_ = json.Unmarshal(b, &existing)
	}
	// Upsert by StartTimestamp and Exchange
	replaced := false
	for i := range existing {
		if existing[i].StartTimestamp == bundle.StartTimestamp && strings.EqualFold(existing[i].Exchange, bundle.Exchange) {
			existing[i] = bundle
			replaced = true
			break
		}
	}
	if !replaced {
		existing = append(existing, bundle)
	}
	// Sort by StartTimestamp asc, then by Exchange for determinism
	sort.Slice(existing, func(i, j int) bool {
		if existing[i].StartTimestamp == existing[j].StartTimestamp {
			return existing[i].Exchange < existing[j].Exchange
		}
		return existing[i].StartTimestamp < existing[j].StartTimestamp
	})

	data, _ := json.MarshalIndent(existing, "", "  ")
	_ = os.WriteFile(path, append(data, '\n'), 0o644)
}

// ======================= Listing JSON helpers =======================

func readCache(path string) ([]cacheItem, map[string]struct{}) {
	f, err := os.Open(path)
	if err != nil {
		return []cacheItem{}, map[string]struct{}{}
	}
	defer f.Close()

	dec := json.NewDecoder(f)
	dec.DisallowUnknownFields()
	var obj listingDocument
	if err := dec.Decode(&obj); err == nil && obj.Items != nil {
		items := obj.Items
		set := make(map[string]struct{}, len(items))
		for i := range items {
			set[items[i].Href] = struct{}{}
		}
		return items, set
	}
	if _, err := f.Seek(0, 0); err == nil {
		var items []cacheItem
		dec2 := json.NewDecoder(f)
		if err2 := dec2.Decode(&items); err2 == nil {
			set := make(map[string]struct{}, len(items))
			for i := range items {
				set[items[i].Href] = struct{}{}
			}
			return items, set
		}
	}
	return []cacheItem{}, map[string]struct{}{}
}

func writeCache(path string, items []cacheItem) error {
	doc := listingDocument{Items: items}
	data, err := json.MarshalIndent(doc, "", "  ")
	if err != nil {
		return err
	}
	data = append(data, '\n')
	dir := filepath.Dir(path)
	tmp, err := os.CreateTemp(dir, "listing.json.*.tmp")
	if err != nil {
		return err
	}
	defer func() { _ = os.Remove(tmp.Name()) }()
	if _, err := tmp.Write(data); err != nil {
		_ = tmp.Close()
		return err
	}
	if err := tmp.Sync(); err != nil {
		_ = tmp.Close()
		return err
	}
	if err := tmp.Close(); err != nil {
		return err
	}
	return os.Rename(tmp.Name(), path)
}

func mergeUniqueSorted(a, b []string) []string {
	seen := make(map[string]struct{}, len(a)+len(b))
	out := make([]string, 0, len(a)+len(b))
	for _, s := range a {
		if _, ok := seen[s]; !ok {
			seen[s] = struct{}{}
			out = append(out, s)
		}
	}
	for _, s := range b {
		if _, ok := seen[s]; !ok {
			seen[s] = struct{}{}
			out = append(out, s)
		}
	}
	sort.Strings(out)
	return out
}

func equalStringSlices(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

// ======================= Exchange index (from recheck) =======================

func buildExchangeIndex(client *http.Client) map[string][]string {
	start := time.Now()
	idx := make(map[string][]string)

	if bases, err := fetchBybitSpotBases(client); err == nil {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "bybit")
		}
	} else {
		log.Printf("warn: bybit spot: %v", err)
	}
	if bases, err := fetchBybitSwapBases(client); err == nil {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "bybit")
		}
	} else {
		log.Printf("warn: bybit swap: %v", err)
	}
	if bases, err := fetchBingXSpotBases(client); err == nil {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "bingx")
		}
	} else {
		log.Printf("warn: bingx spot: %v", err)
	}
	if bases, err := fetchBingXSwapBases(client); err == nil {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "bingx")
		}
	} else {
		log.Printf("warn: bingx swap: %v", err)
	}
	if bases, err := fetchBinanceSpotBases(client); err == nil {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "binance")
		}
	} else {
		log.Printf("warn: binance spot: %v", err)
	}
	if bases, err := fetchBinanceFuturesBases(client); err == nil {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "binance")
		}
	} else {
		log.Printf("warn: binance futures: %v", err)
	}
	if bases, err := fetchMexcSpotBases(client); err == nil {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "mexc")
		}
	} else {
		log.Printf("warn: mexc spot: %v", err)
	}
	if bases, err := fetchMexcSwapBases(client); err == nil {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "mexc")
		}
	} else {
		log.Printf("warn: mexc swap: %v", err)
	}
	if bases, err := fetchBitgetSpotBases(client); err == nil {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "bitget")
		}
	} else {
		log.Printf("warn: bitget spot: %v", err)
	}
	if bases, err := fetchBitgetSwapBases(client); err == nil {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "bitget")
		}
	} else {
		log.Printf("warn: bitget swap: %v", err)
	}
	for k := range idx {
		sort.Strings(idx[k])
	}
	log.Printf("exchanges: built index for %d symbols in %v", len(idx), time.Since(start).Truncate(time.Millisecond))
	return idx
}

func uniqueAppend(list []string, v string) []string {
	for _, s := range list {
		if s == v {
			return list
		}
	}
	return append(list, v)
}

// ==== external index helpers (copied minimal variants) ====

func fetchBybitSpotBases(client *http.Client) (map[string]struct{}, error) {
	req, _ := http.NewRequest(http.MethodGet, "https://api.bybit.com/v5/market/instruments-info?category=spot", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("bybit status %d: %s", res.StatusCode, string(body))
	}
	var out struct {
		Result struct {
			List []struct {
				BaseCoin string `json:"baseCoin"`
				Status   string `json:"status"`
			} `json:"list"`
		} `json:"result"`
	}
	if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
		return nil, err
	}
	bases := make(map[string]struct{})
	for _, it := range out.Result.List {
		if strings.EqualFold(it.Status, "Trading") || strings.EqualFold(it.Status, "TRADING") || it.Status == "" {
			b := strings.ToUpper(strings.TrimSpace(it.BaseCoin))
			if b != "" {
				bases[b] = struct{}{}
			}
		}
	}
	return bases, nil
}

func fetchBybitSwapBases(client *http.Client) (map[string]struct{}, error) {
	bases := make(map[string]struct{})
	for _, cat := range []string{"linear", "inverse"} {
		url := "https://api.bybit.com/v5/market/instruments-info?category=" + cat
		req, _ := http.NewRequest(http.MethodGet, url, nil)
		req.Header.Set("User-Agent", "upbit-listing/1.0")
		res, err := client.Do(req)
		if err != nil {
			return nil, err
		}
		if res.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(res.Body)
			res.Body.Close()
			return nil, fmt.Errorf("bybit %s status %d: %s", cat, res.StatusCode, string(body))
		}
		var out struct {
			Result struct {
				List []struct {
					BaseCoin string `json:"baseCoin"`
					Status   string `json:"status"`
				} `json:"list"`
			} `json:"result"`
		}
		if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
			res.Body.Close()
			return nil, err
		}
		res.Body.Close()
		for _, it := range out.Result.List {
			if !strings.EqualFold(it.Status, "Trading") && !strings.EqualFold(it.Status, "TRADING") && it.Status != "" {
				continue
			}
			b := strings.ToUpper(strings.TrimSpace(it.BaseCoin))
			if b != "" {
				bases[b] = struct{}{}
			}
		}
	}
	return bases, nil
}

func fetchBinanceSpotBases(client *http.Client) (map[string]struct{}, error) {
	req, _ := http.NewRequest(http.MethodGet, "https://api.binance.com/api/v3/exchangeInfo", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("binance status %d: %s", res.StatusCode, string(body))
	}
	var out struct {
		Symbols []struct {
			Status               string   `json:"status"`
			BaseAsset            string   `json:"baseAsset"`
			IsSpotTradingAllowed bool     `json:"isSpotTradingAllowed"`
			Permissions          []string `json:"permissions"`
		} `json:"symbols"`
	}
	if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
		return nil, err
	}
	bases := make(map[string]struct{})
	for _, s := range out.Symbols {
		if !strings.EqualFold(s.Status, "TRADING") {
			continue
		}
		allowed := s.IsSpotTradingAllowed
		if !allowed && len(s.Permissions) > 0 {
			for _, p := range s.Permissions {
				if strings.EqualFold(p, "SPOT") {
					allowed = true
					break
				}
			}
		}
		if !allowed {
			continue
		}
		b := strings.ToUpper(strings.TrimSpace(s.BaseAsset))
		if b != "" {
			bases[b] = struct{}{}
		}
	}
	return bases, nil
}

func fetchBinanceFuturesBases(client *http.Client) (map[string]struct{}, error) {
	bases := make(map[string]struct{})
	req, _ := http.NewRequest(http.MethodGet, "https://fapi.binance.com/fapi/v1/exchangeInfo", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err == nil && res.StatusCode == http.StatusOK {
		var out struct {
			Symbols []struct {
				ContractType string `json:"contractType"`
				Status       string `json:"status"`
				BaseAsset    string `json:"baseAsset"`
			} `json:"symbols"`
		}
		if err := json.NewDecoder(res.Body).Decode(&out); err == nil {
			for _, s := range out.Symbols {
				if strings.EqualFold(s.ContractType, "PERPETUAL") && strings.EqualFold(s.Status, "TRADING") {
					b := strings.ToUpper(strings.TrimSpace(s.BaseAsset))
					if b != "" {
						bases[b] = struct{}{}
					}
				}
			}
		}
		res.Body.Close()
	}
	req2, _ := http.NewRequest(http.MethodGet, "https://dapi.binance.com/dapi/v1/exchangeInfo", nil)
	req2.Header.Set("User-Agent", "upbit-listing/1.0")
	res2, err2 := client.Do(req2)
	if err2 == nil && res2.StatusCode == http.StatusOK {
		var out2 struct {
			Symbols []struct {
				ContractType string `json:"contractType"`
				Status       string `json:"status"`
				BaseAsset    string `json:"baseAsset"`
			} `json:"symbols"`
		}
		if err := json.NewDecoder(res2.Body).Decode(&out2); err == nil {
			for _, s := range out2.Symbols {
				if strings.EqualFold(s.ContractType, "PERPETUAL") && strings.EqualFold(s.Status, "TRADING") {
					b := strings.ToUpper(strings.TrimSpace(s.BaseAsset))
					if b != "" {
						bases[b] = struct{}{}
					}
				}
			}
		}
		res2.Body.Close()
	}
	return bases, nil
}

func fetchBingXSpotBases(client *http.Client) (map[string]struct{}, error) {
	req, _ := http.NewRequest(http.MethodGet, "https://open-api.bingx.com/openApi/spot/v1/common/symbols", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("bingx status %d: %s", res.StatusCode, string(body))
	}
	dec := json.NewDecoder(res.Body)
	dec.UseNumber()
	var raw map[string]any
	if err := dec.Decode(&raw); err != nil {
		return nil, err
	}
	bases := make(map[string]struct{})
	data, ok := raw["data"]
	if !ok || data == nil {
		return bases, nil
	}
	switch t := data.(type) {
	case []any:
		for _, v := range t {
			m, _ := v.(map[string]any)
			base := strings.ToUpper(strings.TrimSpace(getString(m, "baseCoin")))
			if base == "" {
				sym := getString(m, "symbol")
				if sym != "" {
					sym = strings.ReplaceAll(sym, "_", "-")
					parts := strings.Split(sym, "-")
					if len(parts) >= 2 {
						base = strings.ToUpper(strings.TrimSpace(parts[0]))
					}
				}
			}
			status := getInt(m, "status")
			if status == 1 && base != "" {
				bases[base] = struct{}{}
			}
		}
	case map[string]any:
		if syms, ok := t["symbols"]; ok {
			if arr, ok := syms.([]any); ok {
				for _, v := range arr {
					m, _ := v.(map[string]any)
					base := strings.ToUpper(strings.TrimSpace(getString(m, "baseAsset")))
					if base == "" {
						base = strings.ToUpper(strings.TrimSpace(getString(m, "baseCoin")))
					}
					if base == "" {
						sym := getString(m, "symbol")
						if sym != "" {
							sym = strings.ReplaceAll(sym, "_", "-")
							parts := strings.Split(sym, "-")
							if len(parts) >= 2 {
								base = strings.ToUpper(strings.TrimSpace(parts[0]))
							}
						}
					}
					statusStr := strings.ToUpper(strings.TrimSpace(getString(m, "status")))
					statusNum := getInt(m, "status")
					active := statusNum == 1 || statusStr == "TRADING" || statusStr == "ONLINE"
					if active && base != "" {
						bases[base] = struct{}{}
					}
				}
			}
		}
	}
	return bases, nil
}

func fetchBingXSwapBases(client *http.Client) (map[string]struct{}, error) {
	bases := make(map[string]struct{})
	req, _ := http.NewRequest(http.MethodGet, "https://open-api.bingx.com/openApi/swap/v2/market/contracts", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("bingx swap status %d: %s", res.StatusCode, string(body))
	}
	var raw map[string]any
	if err := json.NewDecoder(res.Body).Decode(&raw); err != nil {
		return nil, err
	}
	data, ok := raw["data"].([]any)
	if !ok {
		return bases, nil
	}
	for _, v := range data {
		m, _ := v.(map[string]any)
		base := strings.ToUpper(strings.TrimSpace(getString(m, "baseCoin")))
		status := strings.ToUpper(strings.TrimSpace(getString(m, "status")))
		if (status == "TRADING" || status == "ONLINE" || status == "") && base != "" {
			bases[base] = struct{}{}
		}
	}
	return bases, nil
}

func fetchMexcSpotBases(client *http.Client) (map[string]struct{}, error) {
	req, _ := http.NewRequest(http.MethodGet, "https://api.mexc.com/api/v3/exchangeInfo", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("mexc status %d: %s", res.StatusCode, string(body))
	}
	var out struct {
		Symbols []struct {
			Status               string `json:"status"`
			BaseAsset            string `json:"baseAsset"`
			IsSpotTradingAllowed bool   `json:"isSpotTradingAllowed"`
		} `json:"symbols"`
	}
	if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
		return nil, err
	}
	bases := make(map[string]struct{})
	for _, s := range out.Symbols {
		statusUpper := strings.ToUpper(strings.TrimSpace(s.Status))
		active := statusUpper == "TRADING" || statusUpper == "ENABLED" || strings.TrimSpace(s.Status) == "1" || s.IsSpotTradingAllowed
		if !active {
			continue
		}
		b := strings.ToUpper(strings.TrimSpace(s.BaseAsset))
		if b != "" {
			bases[b] = struct{}{}
		}
	}
	return bases, nil
}

func fetchMexcSwapBases(client *http.Client) (map[string]struct{}, error) {
	bases := make(map[string]struct{})
	req, _ := http.NewRequest(http.MethodGet, "https://contract.mexc.com/api/v1/contract/detail", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("mexc swap status %d: %s", res.StatusCode, string(body))
	}
	var raw struct {
		Data []map[string]any `json:"data"`
	}
	if err := json.NewDecoder(res.Body).Decode(&raw); err != nil {
		return nil, err
	}
	for _, m := range raw.Data {
		base := strings.ToUpper(strings.TrimSpace(getString(m, "baseCurrency")))
		if base == "" {
			base = strings.ToUpper(strings.TrimSpace(getString(m, "baseCoin")))
		}
		stateStr := strings.ToUpper(strings.TrimSpace(getString(m, "state")))
		active := false
		if stateStr == "ONLINE" || stateStr == "TRADING" || stateStr == "" {
			active = true
		} else {
			if v, ok := m["state"]; ok {
				switch t := v.(type) {
				case float64:
					active = int(t) == 0
				case json.Number:
					i, _ := t.Int64()
					active = i == 0
				}
			}
		}
		if active && base != "" {
			bases[base] = struct{}{}
		}
	}
	return bases, nil
}

func fetchBitgetSpotBases(client *http.Client) (map[string]struct{}, error) {
	req, _ := http.NewRequest(http.MethodGet, "https://api.bitget.com/api/v2/spot/public/symbols", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	bases := make(map[string]struct{})
	if res.StatusCode == http.StatusOK {
		var out struct {
			Data []struct {
				BaseCoin string `json:"baseCoin"`
				Status   string `json:"status"`
			} `json:"data"`
		}
		if err := json.NewDecoder(res.Body).Decode(&out); err == nil && len(out.Data) > 0 {
			for _, s := range out.Data {
				if strings.EqualFold(s.Status, "online") || strings.EqualFold(s.Status, "trading") || s.Status == "" {
					b := strings.ToUpper(strings.TrimSpace(s.BaseCoin))
					if b != "" {
						bases[b] = struct{}{}
					}
				}
			}
			return bases, nil
		}
	}
	req2, _ := http.NewRequest(http.MethodGet, "https://api.bitget.com/api/spot/v1/public/symbols", nil)
	req2.Header.Set("User-Agent", "upbit-listing/1.0")
	res2, err := client.Do(req2)
	if err != nil {
		return nil, err
	}
	defer res2.Body.Close()
	if res2.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res2.Body)
		return nil, fmt.Errorf("bitget status %d: %s", res2.StatusCode, string(body))
	}
	var out2 struct {
		Data []struct {
			BaseCoin string `json:"baseCoin"`
			Status   string `json:"status"`
		} `json:"data"`
	}
	if err := json.NewDecoder(res2.Body).Decode(&out2); err != nil {
		return nil, err
	}
	for _, s := range out2.Data {
		if strings.EqualFold(s.Status, "online") || strings.EqualFold(s.Status, "trading") || s.Status == "" {
			b := strings.ToUpper(strings.TrimSpace(s.BaseCoin))
			if b != "" {
				bases[b] = struct{}{}
			}
		}
	}
	return bases, nil
}

func fetchBitgetSwapBases(client *http.Client) (map[string]struct{}, error) {
	bases := make(map[string]struct{})
	for _, productType := range []string{"umcbl", "dmcbl", "cmcbl"} {
		url := "https://api.bitget.com/api/mix/v1/market/contracts?productType=" + productType
		req, _ := http.NewRequest(http.MethodGet, url, nil)
		req.Header.Set("User-Agent", "upbit-listing/1.0")
		res, err := client.Do(req)
		if err != nil {
			return nil, err
		}
		if res.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(res.Body)
			res.Body.Close()
			return nil, fmt.Errorf("bitget mix status %d: %s", res.StatusCode, string(body))
		}
		var out struct {
			Data []struct {
				BaseCoin     string `json:"baseCoin"`
				SymbolStatus string `json:"symbolStatus"`
			} `json:"data"`
		}
		if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
			res.Body.Close()
			return nil, err
		}
		res.Body.Close()
		for _, s := range out.Data {
			if strings.EqualFold(s.SymbolStatus, "normal") || s.SymbolStatus == "" {
				b := strings.ToUpper(strings.TrimSpace(s.BaseCoin))
				if b != "" {
					bases[b] = struct{}{}
				}
			}
		}
	}
	return bases, nil
}

// dynamic JSON helpers
func getString(m map[string]any, key string) string {
	if v, ok := m[key]; ok {
		switch t := v.(type) {
		case string:
			return t
		case json.Number:
			return t.String()
		}
	}
	return ""
}
func getInt(m map[string]any, key string) int {
	if v, ok := m[key]; ok {
		switch t := v.(type) {
		case float64:
			return int(t)
		case json.Number:
			i, _ := t.Int64()
			return int(i)
		case string:
			var i int
			fmt.Sscanf(t, "%d", &i)
			return i
		}
	}
	return 0
}

// ======================= Candle fetchers =======================

func fetchCandlesAny(hc *http.Client, base string, startMs int64, order []string, avail map[string]bool) ([]Candle, string) {
	// try preferred available first; if none marked available, try all in order
	preferred := []string{}
	for _, ex := range order {
		if avail[ex] {
			preferred = append(preferred, ex)
		}
	}
	tryList := preferred
	if len(tryList) == 0 {
		tryList = order
	}
	for _, ex := range tryList {
		var candles []Candle
		var err error
		switch ex {
		case "bybit":
			candles, err = fetchCandlesBybit(hc, base, startMs)
		case "bingx":
			candles, err = fetchCandlesBingX(hc, base, startMs)
		case "binance":
			candles, err = fetchCandlesBinance(hc, base, startMs)
		case "mexc":
			candles, err = fetchCandlesMexc(hc, base, startMs)
		case "bitget":
			candles, err = fetchCandlesBitget(hc, base, startMs)
		}
		if err != nil {
			log.Printf("%s: candle fetch error: %v", ex, err)
			continue
		}
		if len(candles) > 0 {
			return candles, ex
		}
	}
	return nil, ""
}

func fetchCandlesBybit(hc *http.Client, base string, startMs int64) ([]Candle, error) {
	// Prefer spot; fallback linear
	for _, cat := range []string{"spot", "linear"} {
		sym := base + "USDT"
		url := fmt.Sprintf("https://api.bybit.com/v5/market/kline?category=%s&symbol=%s&interval=1&start=%d&limit=120", cat, sym, startMs)
		req, _ := http.NewRequest(http.MethodGet, url, nil)
		res, err := hc.Do(req)
		if err != nil {
			continue
		}
		b, _ := io.ReadAll(res.Body)
		res.Body.Close()
		if res.StatusCode != http.StatusOK {
			continue
		}
		var out struct {
			RetCode int    `json:"retCode"`
			RetMsg  string `json:"retMsg"`
			Result  struct {
				List [][]string `json:"list"`
			} `json:"result"`
		}
		if err := json.Unmarshal(b, &out); err != nil || out.RetCode != 0 {
			continue
		}
		candles := make([]Candle, 0, len(out.Result.List))
		for _, row := range out.Result.List {
			if len(row) < 6 {
				continue
			}
			var c Candle
			fmt.Sscanf(row[0], "%d", &c.T)
			fmt.Sscanf(row[1], "%f", &c.O)
			fmt.Sscanf(row[2], "%f", &c.H)
			fmt.Sscanf(row[3], "%f", &c.L)
			fmt.Sscanf(row[4], "%f", &c.C)
			fmt.Sscanf(row[5], "%f", &c.V)
			candles = append(candles, c)
		}
		return normalizeCandles(candles, startMs), nil
	}
	return nil, errors.New("bybit: no data")
}

func fetchCandlesBingX(hc *http.Client, base string, startMs int64) ([]Candle, error) {
	// v3 first
	for _, path := range []string{
		"https://open-api.bingx.com/openApi/spot/v3/market/kline?symbol=%s&interval=1m&startTime=%d&limit=120",
		"https://open-api.bingx.com/openApi/spot/v1/market/kline?symbol=%s&interval=1m&startTime=%d&limit=120",
	} {
		sym := base + "-USDT"
		url := fmt.Sprintf(path, sym, startMs)
		req, _ := http.NewRequest(http.MethodGet, url, nil)
		res, err := hc.Do(req)
		if err != nil {
			continue
		}
		b, _ := io.ReadAll(res.Body)
		res.Body.Close()
		if res.StatusCode != http.StatusOK {
			continue
		}
		// Try data formats
		var out1 struct {
			Data struct {
				Klines [][]any `json:"klines"`
			} `json:"data"`
		}
		if json.Unmarshal(b, &out1) == nil && len(out1.Data.Klines) > 0 {
			return parseBingxKlines(out1.Data.Klines, startMs), nil
		}
		var out2 struct {
			Data [][]any `json:"data"`
		}
		if json.Unmarshal(b, &out2) == nil && len(out2.Data) > 0 {
			return parseBingxKlines(out2.Data, startMs), nil
		}
	}
	return nil, errors.New("bingx: no data")
}

func parseBingxKlines(rows [][]any, startMs int64) []Candle {
	candles := make([]Candle, 0, len(rows))
	for _, r := range rows {
		if len(r) < 6 {
			continue
		}
		var c Candle
		switch t := r[0].(type) {
		case float64:
			c.T = int64(t)
		case json.Number:
			v, _ := t.Int64()
			c.T = v
		case string:
			fmt.Sscanf(t, "%d", &c.T)
		}
		parseAnyFloat(r[1], &c.O)
		parseAnyFloat(r[2], &c.H)
		parseAnyFloat(r[3], &c.L)
		parseAnyFloat(r[4], &c.C)
		parseAnyFloat(r[5], &c.V)
		candles = append(candles, c)
	}
	return normalizeCandles(candles, startMs)
}

func parseAnyFloat(v any, dst *float64) {
	switch t := v.(type) {
	case float64:
		*dst = t
	case json.Number:
		f, _ := t.Float64()
		*dst = f
	case string:
		fmt.Sscanf(t, "%f", dst)
	}
}

func normalizeCandles(c []Candle, startMs int64) []Candle {
	// ensure sorted by time asc and start aligned to >= startMs, limited to 120
	sort.Slice(c, func(i, j int) bool { return c[i].T < c[j].T })
	out := make([]Candle, 0, 120)
	for _, k := range c {
		if k.T >= startMs {
			out = append(out, k)
		}
	}
	if len(out) > 120 {
		out = out[:120]
	}
	return out
}

func addProfitPercent(c []Candle) []Candle {
	if len(c) == 0 {
		return c
	}
	max := 120
	if len(c) < max {
		max = len(c)
	}
	// compute for bases at 1m, 2m, 3m if exist
	bases := make([]float64, 4)
	// 0m base is open of the first candle
	bases[0] = c[0].O
	// 1m..3m bases are highs of 1st..3rd candles
	for b := 1; b <= 3; b++ {
		if b < max {
			bases[b] = c[b-1].H
		} else {
			bases[b] = 0
		}
	}
	for i := 0; i < max; i++ {
		if bases[0] > 0 && !math.IsNaN(bases[0]) && !math.IsInf(bases[0], 0) {
			c[i].ProfitPercent0m = (c[i].L - bases[0]) / bases[0] * 100
		}
		if bases[1] > 0 && !math.IsNaN(bases[1]) && !math.IsInf(bases[1], 0) {
			c[i].ProfitPercent1m = (c[i].L - bases[1]) / bases[1] * 100
		}
		if bases[2] > 0 && !math.IsNaN(bases[2]) && !math.IsInf(bases[2], 0) {
			c[i].ProfitPercent2m = (c[i].L - bases[2]) / bases[2] * 100
		}
		if bases[3] > 0 && !math.IsNaN(bases[3]) && !math.IsInf(bases[3], 0) {
			c[i].ProfitPercent3m = (c[i].L - bases[3]) / bases[3] * 100
		}
	}
	return c
}

func fetchCandlesBinance(hc *http.Client, base string, startMs int64) ([]Candle, error) {
	sym := base + "USDT"
	url := fmt.Sprintf("https://api.binance.com/api/v3/klines?symbol=%s&interval=1m&limit=120&startTime=%d", sym, startMs)
	req, _ := http.NewRequest(http.MethodGet, url, nil)
	res, err := hc.Do(req)
	if err != nil {
		return nil, err
	}
	b, _ := io.ReadAll(res.Body)
	res.Body.Close()
	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("binance %s", res.Status)
	}
	var rows [][]any
	if err := json.Unmarshal(b, &rows); err != nil {
		return nil, err
	}
	c := make([]Candle, 0, len(rows))
	for _, r := range rows {
		if len(r) < 6 {
			continue
		}
		var k Candle
		switch t := r[0].(type) {
		case float64:
			k.T = int64(t)
		case json.Number:
			v, _ := t.Int64()
			k.T = v
		}
		parseAnyFloat(r[1], &k.O)
		parseAnyFloat(r[2], &k.H)
		parseAnyFloat(r[3], &k.L)
		parseAnyFloat(r[4], &k.C)
		parseAnyFloat(r[5], &k.V)
		c = append(c, k)
	}
	return normalizeCandles(c, startMs), nil
}

func fetchCandlesMexc(hc *http.Client, base string, startMs int64) ([]Candle, error) {
	sym := base + "USDT"
	url := fmt.Sprintf("https://api.mexc.com/api/v3/klines?symbol=%s&interval=1m&limit=120&startTime=%d", sym, startMs)
	req, _ := http.NewRequest(http.MethodGet, url, nil)
	res, err := hc.Do(req)
	if err != nil {
		return nil, err
	}
	b, _ := io.ReadAll(res.Body)
	res.Body.Close()
	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("mexc %s", res.Status)
	}
	var rows [][]any
	if err := json.Unmarshal(b, &rows); err != nil {
		return nil, err
	}
	c := make([]Candle, 0, len(rows))
	for _, r := range rows {
		if len(r) < 6 {
			continue
		}
		var k Candle
		switch t := r[0].(type) {
		case float64:
			k.T = int64(t)
		case json.Number:
			v, _ := t.Int64()
			k.T = v
		}
		parseAnyFloat(r[1], &k.O)
		parseAnyFloat(r[2], &k.H)
		parseAnyFloat(r[3], &k.L)
		parseAnyFloat(r[4], &k.C)
		parseAnyFloat(r[5], &k.V)
		c = append(c, k)
	}
	return normalizeCandles(c, startMs), nil
}

func fetchCandlesBitget(hc *http.Client, base string, startMs int64) ([]Candle, error) {
	sym := base + "USDT"
	// v2 endpoint first
	url := fmt.Sprintf("https://api.bitget.com/api/v2/spot/market/candles?symbol=%s&granularity=60&limit=120&startTime=%d", sym, startMs)
	req, _ := http.NewRequest(http.MethodGet, url, nil)
	res, err := hc.Do(req)
	if err == nil {
		b, _ := io.ReadAll(res.Body)
		res.Body.Close()
		if res.StatusCode == http.StatusOK {
			var out struct {
				Data [][]string `json:"data"`
			}
			if json.Unmarshal(b, &out) == nil && len(out.Data) > 0 {
				candles := make([]Candle, 0, len(out.Data))
				for _, r := range out.Data {
					if len(r) < 6 {
						continue
					}
					var k Candle
					fmt.Sscanf(r[0], "%d", &k.T)
					fmt.Sscanf(r[1], "%f", &k.O)
					fmt.Sscanf(r[2], "%f", &k.H)
					fmt.Sscanf(r[3], "%f", &k.L)
					fmt.Sscanf(r[4], "%f", &k.C)
					fmt.Sscanf(r[5], "%f", &k.V)
					candles = append(candles, k)
				}
				return normalizeCandles(candles, startMs), nil
			}
		}
	}
	// fallback v1
	url = fmt.Sprintf("https://api.bitget.com/api/spot/v1/market/candles?symbol=%s&granularity=60&limit=120", sym)
	req, _ = http.NewRequest(http.MethodGet, url, nil)
	res, err = hc.Do(req)
	if err != nil {
		return nil, err
	}
	b, _ := io.ReadAll(res.Body)
	res.Body.Close()
	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("bitget %s", res.Status)
	}
	var out struct {
		Data [][]string `json:"data"`
	}
	if err := json.Unmarshal(b, &out); err != nil {
		return nil, err
	}
	candles := make([]Candle, 0, len(out.Data))
	for _, r := range out.Data {
		if len(r) < 6 {
			continue
		}
		var k Candle
		fmt.Sscanf(r[0], "%d", &k.T)
		fmt.Sscanf(r[1], "%f", &k.O)
		fmt.Sscanf(r[2], "%f", &k.H)
		fmt.Sscanf(r[3], "%f", &k.L)
		fmt.Sscanf(r[4], "%f", &k.C)
		fmt.Sscanf(r[5], "%f", &k.V)
		candles = append(candles, k)
	}
	return normalizeCandles(candles, startMs), nil
}
