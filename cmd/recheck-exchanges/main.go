package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"
)

func printStartupBanner(name string) {
	fmt.Println("# " + name)
	fmt.Println("Version: 0.0.1")
	fmt.Println("Build: " + buildDate)
	fmt.Println("Commit: " + commitHash)
	fmt.Println("Author: https://t.me/suenot")
	fmt.Println("---")
	flag.PrintDefaults()
}

var buildDate = "unknown"
var commitHash = "unknown"

// removed: apiResponse/apiData/notice (unused)

type cacheItem struct {
	DateUTC                    string    `json:"date_utc,omitempty"`
	DateMSK                    string    `json:"date_msk,omitempty"`
	DateKST                    string    `json:"date_kst,omitempty"`
	DateLegacy                 string    `json:"date,omitempty"`
	Category                   string    `json:"category,omitempty"`
	ListedAt                   string    `json:"listed_at,omitempty"`
	FirstListedAt              string    `json:"first_listed_at,omitempty"`
	ListedAtTimestamp          int64     `json:"listed_at_timestamp,omitempty"`
	FirstListedAtTimestamp     int64     `json:"first_listed_at_timestamp,omitempty"`
	ID                         int       `json:"id,omitempty"`
	Href                       string    `json:"href"`
	Symbol                     string    `json:"symbol"`
	Title                      string    `json:"title"`
	Listing                    bool      `json:"listing"`
	SavedAtTimestamp           int64     `json:"saved_at_timestamp,omitempty"`
	Timestamp                  int64     `json:"-"`
	Exchanges                  []string  `json:"exchanges,omitempty"`
	MinuteReturnsListedAt      []float64 `json:"minute_returns_listed_at,omitempty"`
	MinuteReturnsFirstListedAt []float64 `json:"minute_returns_first_listed_at,omitempty"`
}

type listingDocument struct {
	Items          []cacheItem `json:"items"`
	LatestID       int         `json:"latest_id,omitempty"`
	LatestHref     string      `json:"latest_href,omitempty"`
	LatestListedAt string      `json:"latest_listed_at,omitempty"`
	SavedAt        string      `json:"saved_at,omitempty"`
}

func main() {
	printStartupBanner("recheck-exchanges")
	flag.Parse()
	cwd, _ := os.Getwd()
	outPath := filepath.Join(cwd, "listing.json")

	items, _ := readCache(outPath)
	log.Printf("recheck: loaded %d items from %s", len(items), outPath)

	client := &http.Client{Timeout: 20 * time.Second}
	exIndex := buildExchangeIndex(client)

	flagUpdated := 0
	exUpdated := 0
	seenBingx := make(map[string]bool)
	seenMexc := make(map[string]bool)

	for i := range items {
		listingNow := isListingTitle(items[i].Title)
		if listingNow != items[i].Listing {
			items[i].Listing = listingNow
			flagUpdated++
		}
		if items[i].Listing && items[i].Symbol != "" {
			current := items[i].Exchanges
			base := strings.ToUpper(strings.TrimSpace(items[i].Symbol))
			if ex := exIndex[base]; len(ex) > 0 {
				current = mergeUniqueSorted(current, ex)
			}
			if !containsString(current, "bingx") {
				ok, has := seenBingx[base]
				if !has {
					ok = hasBingXSpotBaseQuick(client, base)
					seenBingx[base] = ok
				}
				if ok {
					current = uniqueAppend(current, "bingx")
				}
			}
			if !containsString(current, "mexc") {
				ok, has := seenMexc[base]
				if !has {
					ok = hasMexcSpotBaseQuick(client, base)
					seenMexc[base] = ok
				}
				if ok {
					current = uniqueAppend(current, "mexc")
				}
			}
			sort.Strings(current)
			if !equalStringSlices(items[i].Exchanges, current) {
				items[i].Exchanges = current
				exUpdated++
			}
		}
	}

	if flagUpdated > 0 || exUpdated > 0 {
		if err := writeCache(outPath, items); err != nil {
			fmt.Fprintf(os.Stderr, "failed to write cache after recheck: %v\n", err)
			os.Exit(1)
		}
		log.Printf("recheck: updated listing flags: %d, exchanges: %d", flagUpdated, exUpdated)
	}
	fmt.Printf("Rechecked listing flags and exchanges for %d items.\n", len(items))
}

func readCache(path string) ([]cacheItem, map[string]struct{}) {
	f, err := os.Open(path)
	if err != nil {
		return []cacheItem{}, map[string]struct{}{}
	}
	defer f.Close()

	dec := json.NewDecoder(f)
	dec.DisallowUnknownFields()
	var obj listingDocument
	if err := dec.Decode(&obj); err == nil && obj.Items != nil {
		items := obj.Items
		set := make(map[string]struct{}, len(items))
		for i := range items {
			set[items[i].Href] = struct{}{}
		}
		return items, set
	}
	if _, err := f.Seek(0, 0); err == nil {
		var items []cacheItem
		dec2 := json.NewDecoder(f)
		if err2 := dec2.Decode(&items); err2 == nil {
			set := make(map[string]struct{}, len(items))
			for i := range items {
				set[items[i].Href] = struct{}{}
			}
			return items, set
		}
	}
	return []cacheItem{}, map[string]struct{}{}
}

func writeCache(path string, items []cacheItem) error {
	doc := listingDocument{Items: items}
	data, err := json.MarshalIndent(doc, "", "  ")
	if err != nil {
		return err
	}
	data = append(data, '\n')
	dir := filepath.Dir(path)
	tmp, err := os.CreateTemp(dir, "listing.json.*.tmp")
	if err != nil {
		return err
	}
	defer func() { _ = os.Remove(tmp.Name()) }()
	if _, err := tmp.Write(data); err != nil {
		_ = tmp.Close()
		return err
	}
	if err := tmp.Sync(); err != nil {
		_ = tmp.Close()
		return err
	}
	if err := tmp.Close(); err != nil {
		return err
	}
	return os.Rename(tmp.Name(), path)
}

func isListingTitle(title string) bool {
	return strings.Contains(title, "Market Support for")
}

func buildExchangeIndex(client *http.Client) map[string][]string {
	start := time.Now()
	idx := make(map[string][]string)

	if bases, err := fetchBybitSpotBases(client); err != nil {
		log.Printf("warn: bybit fetch failed: %v", err)
	} else {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "bybit")
		}
	}
	if bases, err := fetchBybitSwapBases(client); err != nil {
		log.Printf("warn: bybit swap fetch failed: %v", err)
	} else {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "bybit")
		}
	}
	if bases, err := fetchBinanceSpotBases(client); err != nil {
		log.Printf("warn: binance fetch failed: %v", err)
	} else {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "binance")
		}
	}
	if bases, err := fetchBinanceFuturesBases(client); err != nil {
		log.Printf("warn: binance futures fetch failed: %v", err)
	} else {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "binance")
		}
	}
	if bases, err := fetchBingXSpotBases(client); err != nil {
		log.Printf("warn: bingx fetch failed: %v", err)
	} else {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "bingx")
		}
	}
	if bases, err := fetchBingXSwapBases(client); err != nil {
		log.Printf("warn: bingx swap fetch failed: %v", err)
	} else {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "bingx")
		}
	}
	if bases, err := fetchBitgetSpotBases(client); err != nil {
		log.Printf("warn: bitget fetch failed: %v", err)
	} else {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "bitget")
		}
	}
	if bases, err := fetchBitgetSwapBases(client); err != nil {
		log.Printf("warn: bitget swap fetch failed: %v", err)
	} else {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "bitget")
		}
	}
	if bases, err := fetchMexcSpotBases(client); err != nil {
		log.Printf("warn: mexc fetch failed: %v", err)
	} else {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "mexc")
		}
	}
	if bases, err := fetchMexcSwapBases(client); err != nil {
		log.Printf("warn: mexc swap fetch failed: %v", err)
	} else {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "mexc")
		}
	}
	if bases, err := fetchKucoinSpotBases(client); err != nil {
		log.Printf("warn: kucoin fetch failed: %v", err)
	} else {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "kucoin")
		}
	}
	if bases, err := fetchKucoinFuturesBases(client); err != nil {
		log.Printf("warn: kucoin futures fetch failed: %v", err)
	} else {
		for b := range bases {
			idx[b] = uniqueAppend(idx[b], "kucoin")
		}
	}
	for k := range idx {
		sort.Strings(idx[k])
	}
	log.Printf("exchanges: built index for %d symbols in %v", len(idx), time.Since(start).Truncate(time.Millisecond))
	return idx
}

func uniqueAppend(list []string, v string) []string {
	for _, s := range list {
		if s == v {
			return list
		}
	}
	return append(list, v)
}

func mergeUniqueSorted(a, b []string) []string {
	seen := make(map[string]struct{}, len(a)+len(b))
	out := make([]string, 0, len(a)+len(b))
	for _, s := range a {
		if _, ok := seen[s]; !ok {
			seen[s] = struct{}{}
			out = append(out, s)
		}
	}
	for _, s := range b {
		if _, ok := seen[s]; !ok {
			seen[s] = struct{}{}
			out = append(out, s)
		}
	}
	sort.Strings(out)
	return out
}

func equalStringSlices(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

func containsString(list []string, v string) bool {
	for _, s := range list {
		if s == v {
			return true
		}
	}
	return false
}

// External exchange queries below (copied minimal variants from primary app)

func fetchBybitSpotBases(client *http.Client) (map[string]struct{}, error) {
	req, _ := http.NewRequest(http.MethodGet, "https://api.bybit.com/v5/market/instruments-info?category=spot", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("bybit status %d: %s", res.StatusCode, string(body))
	}
	var out struct {
		RetCode int    `json:"retCode"`
		RetMsg  string `json:"retMsg"`
		Result  struct {
			List []struct {
				Symbol    string `json:"symbol"`
				BaseCoin  string `json:"baseCoin"`
				QuoteCoin string `json:"quoteCoin"`
				Status    string `json:"status"`
			} `json:"list"`
		} `json:"result"`
	}
	if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
		return nil, err
	}
	bases := make(map[string]struct{})
	for _, it := range out.Result.List {
		if strings.EqualFold(it.Status, "Trading") || strings.EqualFold(it.Status, "TRADING") || it.Status == "" {
			b := strings.ToUpper(strings.TrimSpace(it.BaseCoin))
			if b != "" {
				bases[b] = struct{}{}
			}
		}
	}
	return bases, nil
}

func fetchBybitSwapBases(client *http.Client) (map[string]struct{}, error) {
	bases := make(map[string]struct{})
	for _, cat := range []string{"linear", "inverse"} {
		url := "https://api.bybit.com/v5/market/instruments-info?category=" + cat
		req, _ := http.NewRequest(http.MethodGet, url, nil)
		req.Header.Set("User-Agent", "upbit-listing/1.0")
		res, err := client.Do(req)
		if err != nil {
			return nil, err
		}
		if res.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(res.Body)
			res.Body.Close()
			return nil, fmt.Errorf("bybit %s status %d: %s", cat, res.StatusCode, string(body))
		}
		var out struct {
			Result struct {
				List []struct {
					Symbol       string `json:"symbol"`
					BaseCoin     string `json:"baseCoin"`
					Status       string `json:"status"`
					ContractType string `json:"contractType"`
				} `json:"list"`
			} `json:"result"`
		}
		if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
			res.Body.Close()
			return nil, err
		}
		res.Body.Close()
		for _, it := range out.Result.List {
			if !strings.EqualFold(it.Status, "Trading") && !strings.EqualFold(it.Status, "TRADING") && it.Status != "" {
				continue
			}
			b := strings.ToUpper(strings.TrimSpace(it.BaseCoin))
			if b != "" {
				bases[b] = struct{}{}
			}
		}
	}
	return bases, nil
}

func fetchBinanceSpotBases(client *http.Client) (map[string]struct{}, error) {
	req, _ := http.NewRequest(http.MethodGet, "https://api.binance.com/api/v3/exchangeInfo", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("binance status %d: %s", res.StatusCode, string(body))
	}
	var out struct {
		Symbols []struct {
			Symbol               string   `json:"symbol"`
			Status               string   `json:"status"`
			BaseAsset            string   `json:"baseAsset"`
			QuoteAsset           string   `json:"quoteAsset"`
			IsSpotTradingAllowed bool     `json:"isSpotTradingAllowed"`
			Permissions          []string `json:"permissions"`
		} `json:"symbols"`
	}
	if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
		return nil, err
	}
	bases := make(map[string]struct{})
	for _, s := range out.Symbols {
		if !strings.EqualFold(s.Status, "TRADING") {
			continue
		}
		allowed := s.IsSpotTradingAllowed
		if !allowed && len(s.Permissions) > 0 {
			for _, p := range s.Permissions {
				if strings.EqualFold(p, "SPOT") {
					allowed = true
					break
				}
			}
		}
		if !allowed {
			continue
		}
		b := strings.ToUpper(strings.TrimSpace(s.BaseAsset))
		if b != "" {
			bases[b] = struct{}{}
		}
	}
	return bases, nil
}

func fetchBinanceFuturesBases(client *http.Client) (map[string]struct{}, error) {
	bases := make(map[string]struct{})
	req, _ := http.NewRequest(http.MethodGet, "https://fapi.binance.com/fapi/v1/exchangeInfo", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err == nil && res.StatusCode == http.StatusOK {
		var out struct {
			Symbols []struct {
				ContractType string `json:"contractType"`
				Status       string `json:"status"`
				BaseAsset    string `json:"baseAsset"`
			} `json:"symbols"`
		}
		if err := json.NewDecoder(res.Body).Decode(&out); err == nil {
			for _, s := range out.Symbols {
				if strings.EqualFold(s.ContractType, "PERPETUAL") && strings.EqualFold(s.Status, "TRADING") {
					b := strings.ToUpper(strings.TrimSpace(s.BaseAsset))
					if b != "" {
						bases[b] = struct{}{}
					}
				}
			}
		}
		res.Body.Close()
	}
	req2, _ := http.NewRequest(http.MethodGet, "https://dapi.binance.com/dapi/v1/exchangeInfo", nil)
	req2.Header.Set("User-Agent", "upbit-listing/1.0")
	res2, err2 := client.Do(req2)
	if err2 == nil && res2.StatusCode == http.StatusOK {
		var out2 struct {
			Symbols []struct {
				ContractType string `json:"contractType"`
				Status       string `json:"status"`
				BaseAsset    string `json:"baseAsset"`
			} `json:"symbols"`
		}
		if err := json.NewDecoder(res2.Body).Decode(&out2); err == nil {
			for _, s := range out2.Symbols {
				if strings.EqualFold(s.ContractType, "PERPETUAL") && strings.EqualFold(s.Status, "TRADING") {
					b := strings.ToUpper(strings.TrimSpace(s.BaseAsset))
					if b != "" {
						bases[b] = struct{}{}
					}
				}
			}
		}
		res2.Body.Close()
	}
	return bases, nil
}

func fetchBingXSpotBases(client *http.Client) (map[string]struct{}, error) {
	req, _ := http.NewRequest(http.MethodGet, "https://open-api.bingx.com/openApi/spot/v1/common/symbols", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("bingx status %d: %s", res.StatusCode, string(body))
	}
	dec := json.NewDecoder(res.Body)
	dec.UseNumber()
	var raw map[string]any
	if err := dec.Decode(&raw); err != nil {
		return nil, err
	}
	bases := make(map[string]struct{})
	data, ok := raw["data"]
	if !ok || data == nil {
		return bases, nil
	}
	switch t := data.(type) {
	case []any:
		for _, v := range t {
			m, _ := v.(map[string]any)
			base := strings.ToUpper(strings.TrimSpace(getString(m, "baseCoin")))
			if base == "" {
				sym := getString(m, "symbol")
				if sym != "" {
					sym = strings.ReplaceAll(sym, "_", "-")
					parts := strings.Split(sym, "-")
					if len(parts) >= 2 {
						base = strings.ToUpper(strings.TrimSpace(parts[0]))
					}
				}
			}
			status := getInt(m, "status")
			if status == 1 && base != "" {
				bases[base] = struct{}{}
			}
		}
	case map[string]any:
		if syms, ok := t["symbols"]; ok {
			if arr, ok := syms.([]any); ok {
				for _, v := range arr {
					m, _ := v.(map[string]any)
					base := strings.ToUpper(strings.TrimSpace(getString(m, "baseAsset")))
					if base == "" {
						base = strings.ToUpper(strings.TrimSpace(getString(m, "baseCoin")))
					}
					if base == "" {
						sym := getString(m, "symbol")
						if sym != "" {
							sym = strings.ReplaceAll(sym, "_", "-")
							parts := strings.Split(sym, "-")
							if len(parts) >= 2 {
								base = strings.ToUpper(strings.TrimSpace(parts[0]))
							}
						}
					}
					statusStr := strings.ToUpper(strings.TrimSpace(getString(m, "status")))
					statusNum := getInt(m, "status")
					active := statusNum == 1 || statusStr == "TRADING" || statusStr == "ONLINE"
					if active && base != "" {
						bases[base] = struct{}{}
					}
				}
			}
		}
	}
	return bases, nil
}

func fetchBingXSwapBases(client *http.Client) (map[string]struct{}, error) {
	bases := make(map[string]struct{})
	req, _ := http.NewRequest(http.MethodGet, "https://open-api.bingx.com/openApi/swap/v2/market/contracts", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("bingx swap status %d: %s", res.StatusCode, string(body))
	}
	var raw map[string]any
	if err := json.NewDecoder(res.Body).Decode(&raw); err != nil {
		return nil, err
	}
	data, ok := raw["data"].([]any)
	if !ok {
		return bases, nil
	}
	for _, v := range data {
		m, _ := v.(map[string]any)
		base := strings.ToUpper(strings.TrimSpace(getString(m, "baseCoin")))
		status := strings.ToUpper(strings.TrimSpace(getString(m, "status")))
		if (status == "TRADING" || status == "ONLINE" || status == "") && base != "" {
			bases[base] = struct{}{}
		}
	}
	return bases, nil
}

func fetchBitgetSpotBases(client *http.Client) (map[string]struct{}, error) {
	req, _ := http.NewRequest(http.MethodGet, "https://api.bitget.com/api/v2/spot/public/symbols", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	bases := make(map[string]struct{})
	if res.StatusCode == http.StatusOK {
		var out struct {
			Code string `json:"code"`
			Data []struct {
				Symbol    string `json:"symbol"`
				BaseCoin  string `json:"baseCoin"`
				QuoteCoin string `json:"quoteCoin"`
				Status    string `json:"status"`
			} `json:"data"`
		}
		if err := json.NewDecoder(res.Body).Decode(&out); err == nil && len(out.Data) > 0 {
			for _, s := range out.Data {
				if strings.EqualFold(s.Status, "online") || strings.EqualFold(s.Status, "trading") || s.Status == "" {
					b := strings.ToUpper(strings.TrimSpace(s.BaseCoin))
					if b != "" {
						bases[b] = struct{}{}
					}
				}
			}
			return bases, nil
		}
	}
	req2, _ := http.NewRequest(http.MethodGet, "https://api.bitget.com/api/spot/v1/public/symbols", nil)
	req2.Header.Set("User-Agent", "upbit-listing/1.0")
	res2, err := client.Do(req2)
	if err != nil {
		return nil, err
	}
	defer res2.Body.Close()
	if res2.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res2.Body)
		return nil, fmt.Errorf("bitget status %d: %s", res2.StatusCode, string(body))
	}
	var out2 struct {
		Code string `json:"code"`
		Data []struct {
			Symbol    string `json:"symbol"`
			BaseCoin  string `json:"baseCoin"`
			QuoteCoin string `json:"quoteCoin"`
			Status    string `json:"status"`
		} `json:"data"`
	}
	if err := json.NewDecoder(res2.Body).Decode(&out2); err != nil {
		return nil, err
	}
	for _, s := range out2.Data {
		if strings.EqualFold(s.Status, "online") || strings.EqualFold(s.Status, "trading") || s.Status == "" {
			b := strings.ToUpper(strings.TrimSpace(s.BaseCoin))
			if b != "" {
				bases[b] = struct{}{}
			}
		}
	}
	return bases, nil
}

func fetchBitgetSwapBases(client *http.Client) (map[string]struct{}, error) {
	bases := make(map[string]struct{})
	for _, productType := range []string{"umcbl", "dmcbl", "cmcbl"} {
		url := "https://api.bitget.com/api/mix/v1/market/contracts?productType=" + productType
		req, _ := http.NewRequest(http.MethodGet, url, nil)
		req.Header.Set("User-Agent", "upbit-listing/1.0")
		res, err := client.Do(req)
		if err != nil {
			return nil, err
		}
		if res.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(res.Body)
			res.Body.Close()
			return nil, fmt.Errorf("bitget mix status %d: %s", res.StatusCode, string(body))
		}
		var out struct {
			Data []struct {
				BaseCoin           string   `json:"baseCoin"`
				Symbol             string   `json:"symbol"`
				SupportMarginCoins []string `json:"supportMarginCoins"`
				SymbolStatus       string   `json:"symbolStatus"`
			} `json:"data"`
		}
		if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
			res.Body.Close()
			return nil, err
		}
		res.Body.Close()
		for _, s := range out.Data {
			if strings.EqualFold(s.SymbolStatus, "normal") || s.SymbolStatus == "" {
				b := strings.ToUpper(strings.TrimSpace(s.BaseCoin))
				if b != "" {
					bases[b] = struct{}{}
				}
			}
		}
	}
	return bases, nil
}

func getString(m map[string]any, key string) string {
	if v, ok := m[key]; ok {
		switch t := v.(type) {
		case string:
			return t
		case json.Number:
			return t.String()
		}
	}
	return ""
}

func getInt(m map[string]any, key string) int {
	if v, ok := m[key]; ok {
		switch t := v.(type) {
		case float64:
			return int(t)
		case json.Number:
			i, _ := t.Int64()
			return int(i)
		case string:
			var i int
			fmt.Sscanf(t, "%d", &i)
			return i
		}
	}
	return 0
}

func fetchMexcSpotBases(client *http.Client) (map[string]struct{}, error) {
	req, _ := http.NewRequest(http.MethodGet, "https://api.mexc.com/api/v3/exchangeInfo", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("mexc status %d: %s", res.StatusCode, string(body))
	}
	var out struct {
		Symbols []struct {
			Symbol               string `json:"symbol"`
			Status               string `json:"status"`
			BaseAsset            string `json:"baseAsset"`
			QuoteAsset           string `json:"quoteAsset"`
			IsSpotTradingAllowed bool   `json:"isSpotTradingAllowed"`
		} `json:"symbols"`
	}
	if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
		return nil, err
	}
	bases := make(map[string]struct{})
	for _, s := range out.Symbols {
		statusUpper := strings.ToUpper(strings.TrimSpace(s.Status))
		active := statusUpper == "TRADING" || statusUpper == "ENABLED" || strings.TrimSpace(s.Status) == "1" || s.IsSpotTradingAllowed
		if !active {
			continue
		}
		b := strings.ToUpper(strings.TrimSpace(s.BaseAsset))
		if b != "" {
			bases[b] = struct{}{}
		}
	}
	return bases, nil
}

func hasBingXSpotBaseQuick(client *http.Client, base string) bool {
	req, _ := http.NewRequest(http.MethodGet, "https://open-api.bingx.com/openApi/spot/v1/common/symbols", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil || res.StatusCode != http.StatusOK {
		if res != nil {
			res.Body.Close()
		}
		return false
	}
	var raw struct {
		Data struct {
			Symbols []struct {
				Symbol string `json:"symbol"`
			} `json:"symbols"`
		} `json:"data"`
	}
	if err := json.NewDecoder(res.Body).Decode(&raw); err != nil {
		res.Body.Close()
		return false
	}
	res.Body.Close()
	prefix := strings.ToUpper(base) + "-"
	for _, s := range raw.Data.Symbols {
		if strings.HasPrefix(strings.ToUpper(s.Symbol), prefix) {
			return true
		}
	}
	return false
}

func hasMexcSpotBaseQuick(client *http.Client, base string) bool {
	req, _ := http.NewRequest(http.MethodGet, "https://api.mexc.com/api/v3/exchangeInfo", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil || res.StatusCode != http.StatusOK {
		if res != nil {
			res.Body.Close()
		}
		return false
	}
	var out struct {
		Symbols []struct {
			BaseAsset            string `json:"baseAsset"`
			IsSpotTradingAllowed bool   `json:"isSpotTradingAllowed"`
		} `json:"symbols"`
	}
	if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
		res.Body.Close()
		return false
	}
	res.Body.Close()
	b := strings.ToUpper(base)
	for _, s := range out.Symbols {
		if strings.ToUpper(s.BaseAsset) == b && s.IsSpotTradingAllowed {
			return true
		}
	}
	return false
}

func fetchMexcSwapBases(client *http.Client) (map[string]struct{}, error) {
	bases := make(map[string]struct{})
	req, _ := http.NewRequest(http.MethodGet, "https://contract.mexc.com/api/v1/contract/detail", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("mexc swap status %d: %s", res.StatusCode, string(body))
	}
	var raw struct {
		Data []map[string]any `json:"data"`
	}
	if err := json.NewDecoder(res.Body).Decode(&raw); err != nil {
		return nil, err
	}
	for _, m := range raw.Data {
		base := strings.ToUpper(strings.TrimSpace(getString(m, "baseCurrency")))
		if base == "" {
			base = strings.ToUpper(strings.TrimSpace(getString(m, "baseCoin")))
		}
		stateStr := strings.ToUpper(strings.TrimSpace(getString(m, "state")))
		active := false
		if stateStr == "ONLINE" || stateStr == "TRADING" || stateStr == "" {
			active = true
		} else {
			if v, ok := m["state"]; ok {
				switch t := v.(type) {
				case float64:
					active = int(t) == 0
				case json.Number:
					i, _ := t.Int64()
					active = i == 0
				}
			}
		}
		if active && base != "" {
			bases[base] = struct{}{}
		}
	}
	return bases, nil
}

func fetchKucoinSpotBases(client *http.Client) (map[string]struct{}, error) {
	req, _ := http.NewRequest(http.MethodGet, "https://api.kucoin.com/api/v2/symbols", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("kucoin status %d: %s", res.StatusCode, string(body))
	}
	var out struct {
		Code string `json:"code"`
		Data []struct {
			BaseCurrency  string `json:"baseCurrency"`
			EnableTrading bool   `json:"enableTrading"`
		} `json:"data"`
	}
	if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
		return nil, err
	}
	bases := make(map[string]struct{})
	for _, s := range out.Data {
		if s.EnableTrading {
			b := strings.ToUpper(strings.TrimSpace(s.BaseCurrency))
			if b != "" {
				bases[b] = struct{}{}
			}
		}
	}
	return bases, nil
}

func fetchKucoinFuturesBases(client *http.Client) (map[string]struct{}, error) {
	req, _ := http.NewRequest(http.MethodGet, "https://api-futures.kucoin.com/api/v1/contracts/active", nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("kucoin futures status %d: %s", res.StatusCode, string(body))
	}
	var out struct {
		Code string `json:"code"`
		Data []struct {
			BaseCurrency string `json:"baseCurrency"`
			Symbol       string `json:"symbol"`
			Status       string `json:"status"`
		} `json:"data"`
	}
	if err := json.NewDecoder(res.Body).Decode(&out); err != nil {
		return nil, err
	}
	bases := make(map[string]struct{})
	for _, s := range out.Data {
		if strings.EqualFold(s.Status, "Open") || s.Status == "" {
			b := strings.ToUpper(strings.TrimSpace(s.BaseCurrency))
			if b != "" {
				bases[b] = struct{}{}
			}
		}
	}
	return bases, nil
}
