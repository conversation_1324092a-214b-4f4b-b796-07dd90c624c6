(() => {
  const pagesToLoad = 5; // сколько страниц подгрузить
  const delay = (ms) => new Promise(r => setTimeout(r, ms));

  const normalize = (s) =>
    (s ?? "").replace(/\u200b/g, "").replace(/\xa0/g, " ").replace(/\s+/g, " ").trim();

  const collapseTickerInsideParens = (s) =>
    s.replace(/\(\s*([A-Za-z0-9\-\s]{1,60})\s*\)/g, (_, inner) => `(${inner.replace(/\s+/g, "")})`);

  const collapseSplitLetters = (s) => {
    const toks = s.split(/\s+/);
    const out = [];
    let buf = [];
    for (const t of toks) {
      if (t.length === 1 && /[A-Za-z]/.test(t)) buf.push(t);
      else {
        if (buf.length) { out.push(buf.join("")); buf = []; }
        out.push(t);
      }
    }
    if (buf.length) out.push(buf.join(""));
    let s2 = out.join(" ");
    s2 = s2.replace(/\s*\(\s*/g, " (").replace(/\s*\)\s*/g, ") ").replace(/\s*,\s*/g, ", ");
    return s2.replace(/\s+/g, " ").trim();
  };

  const PHRASE_RX = /(?:Trade\s+)?Market\s*Support\s*for\s+[^()\n]*\(\s*([A-Z0-9-]{2,20})\s*\)/i;

  const DATE_RXES = [
    /\b\d{4}[./-]\d{1,2}[./-]\d{1,2}(?:\s+\d{1,2}:\d{2})?\b/,
    /\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Sept|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}(?:\s+\d{1,2}:\d{2})?\b/i,
    /\b\d{4}\s*년\s*\d{1,2}\s*월\s*\d{1,2}\s*일(?:\s*\d{1,2}:\d{2})?\b/,
  ];

  const findFirstDate = (t) => {
    const txt = normalize(t);
    for (const rx of DATE_RXES) {
      const m = txt.match(rx);
      if (m) return m[0];
    }
    return null;
  };

  const closestDateInText = (text, anchor) => {
    const txt = normalize(text);
    if (!txt) return null;
    const anchorKey = (anchor.split(" (")[0] || "").slice(0, 40);
    const posAnchor = anchorKey ? txt.indexOf(anchorKey) : -1;
    let best = null, bestDist = Infinity;
    for (const rx of DATE_RXES) {
      let m;
      const re = new RegExp(rx.source, rx.flags + (rx.flags.includes("g") ? "" : "g"));
      while ((m = re.exec(txt))) {
        const pos = m.index;
        const dist = Math.abs(pos - (posAnchor >= 0 ? posAnchor : pos));
        if (dist < bestDist) best = m[0], bestDist = dist;
      }
    }
    return best;
  };

  const clickMoreUntil = async (n) => {
    let loaded = 1;
    for (let i = 0; i < 1000 && loaded < n; i++) {
      // Попробовать кнопку "더보기/More" (вёрстка может меняться — подбираем generically)
      const moreBtn = Array.from(document.querySelectorAll('button, a'))
        .find(el => /더보기|more|더 보 기/i.test(el.textContent || ""));
      // Фолбэк — прокрутка вниз
      window.scrollTo(0, document.body.scrollHeight);
      if (moreBtn) moreBtn.click();
      await delay(900);
      loaded++;
    }
  };

  const extract = () => {
    const items = [];
    const anchors = Array.from(document.querySelectorAll("a"));
    for (const a of anchors) {
      let titleRaw = normalize(a.textContent || "");
      if (!titleRaw) continue;
      let title = collapseSplitLetters(collapseTickerInsideParens(titleRaw));
      if (!PHRASE_RX.test(title)) continue;

      const mTicker = title.match(/\(\s*([A-Z0-9-]{2,20})\s*\)/);
      if (!mTicker) continue;
      const symbol = mTicker[1].toUpperCase();

      let date = null;
      let node = a;
      for (let hop = 0; hop < 6 && node && !date; hop++) {
        node = node.parentElement;
        if (!node) break;
        const parentText = normalize(node.textContent || "");
        const maybe = closestDateInText(parentText, title);
        if (maybe) { date = maybe; break; }
        const prev = node.previousElementSibling;
        const next = node.nextElementSibling;
        if (!date && prev) {
          const t = normalize(prev.textContent || "");
          const m = findFirstDate(t);
          if (m) date = m;
        }
        if (!date && next) {
          const t = normalize(next.textContent || "");
          const m = findFirstDate(t);
          if (m) date = m;
        }
      }
      if (!date) {
        const pageText = normalize(document.body.innerText || document.body.textContent || "");
        const anchorKey = (title.split(" (")[0] || "").slice(0, 40);
        const pos = anchorKey ? pageText.indexOf(anchorKey) : -1;
        if (pos >= 0) {
          const window = pageText.slice(Math.max(0, pos - 2000), pos + 2000);
          date = closestDateInText(window, title) || findFirstDate(window);
        } else {
          date = findFirstDate(pageText);
        }
      }

      const href = (() => {
        const raw = a.getAttribute("href") || "";
        try { return new URL(raw, location.origin).toString(); } catch { return raw; }
      })();

      items.push({ title, symbol, date, href });
    }

    const filtered = items.filter(r => /Market\s*Support\s*for/i.test(r.title));
    const uniq = [];
    const seen = new Set();
    for (const r of filtered) {
      const key = `${r.symbol}::${r.date || ""}::${r.href}`;
      if (seen.has(key)) continue;
      seen.add(key);
      uniq.push(r);
    }
    return uniq;
  };

  (async () => {
    await clickMoreUntil(pagesToLoad);
    const data = extract();
    console.log(JSON.stringify(data, null, 2));
  })();
})();
