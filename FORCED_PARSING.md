# Форсированный парсинг (Forced Parsing)

## Описание

Функциональность форсированного парсинга позволяет активировать дополнительные запросы к источникам данных в определенные моменты времени. Это полезно для более частого мониторинга в критические периоды, когда вероятность появления новых листингов выше.

## Принцип работы

Форсированный парсинг активируется в моменты времени, когда минуты кратны 10:
- xx:00 (начало часа)
- xx:10 
- xx:20
- xx:30
- xx:40
- xx:50

Для каждого такого момента создается временное окно, в котором выполняются дополнительные запросы с повышенной частотой.

## Настройки конфигурации

Для каждого источника данных (`announcements_api`, `markets_api`, `service_center`) можно настроить форсированный парсинг:

```json
{
  "settings": {
    "announcements_api": {
      "enabled": true,
      "cycle_interval_ms": 4800,
      "forced_parsing": {
        "enabled": true,
        "before_ms": 60000,
        "after_ms": 60000,
        "request_interval_ms": 500
      }
    }
  }
}
```

### Параметры настройки

- **`enabled`** (bool): Включает/выключает форсированный парсинг для данного источника
- **`before_ms`** (int): Время в миллисекундах до целевого момента, когда начинается форсированный парсинг
- **`after_ms`** (int): Время в миллисекундах после целевого момента, когда заканчивается форсированный парсинг  
- **`request_interval_ms`** (int): Интервал между дополнительными запросами в миллисекундах во время форсированного парсинга

### Пример временного окна

Для настроек `before_ms: 60000, after_ms: 60000` и целевого времени 20:10:
- Форсированный парсинг начинается в 20:09:00 (за 1 минуту до)
- Продолжается до 20:11:00 (1 минута после)
- Общая длительность: 2 минуты

## Логирование

Система логирует начало и окончание форсированного парсинга:

```
forced-parsing: announcements_api worker started (before=60000ms, after=60000ms, interval=500ms)
forced-parsing: announcements_api starting forced parsing at 20:09:00
forced-parsing: announcements_api stopping forced parsing at 20:11:01
```

## Рекомендуемые настройки

### Для продакшена
```json
"forced_parsing": {
  "enabled": true,
  "before_ms": 60000,    // 1 минута до
  "after_ms": 60000,     // 1 минута после
  "request_interval_ms": 500  // запрос каждые 500мс
}
```

### Для тестирования
```json
"forced_parsing": {
  "enabled": true,
  "before_ms": 30000,    // 30 секунд до
  "after_ms": 30000,     // 30 секунд после
  "request_interval_ms": 1000  // запрос каждую секунду
}
```

## Взаимодействие с другими функциями

- **Interval Tuning**: Форсированный парсинг работает независимо от автоматической настройки интервалов
- **Proxy**: Дополнительные запросы используют ту же систему прокси, что и обычные запросы
- **Rate Limiting**: Учитывайте ограничения API при настройке `request_interval_ms`

## Отключение

Чтобы отключить форсированный парсинг для источника, установите:
```json
"forced_parsing": {
  "enabled": false
}
```

Или полностью удалите секцию `forced_parsing` из конфигурации.
