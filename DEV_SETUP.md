# Dev/Prod Environment Setup

Этот документ описывает настройку системы для работы с dev и prod окружениями.

## Обзор изменений

1. **Dev Server** - отдельный сервер для эмуляции Upbit API
2. **Конфигурация** - поддержка переключения между dev и prod URL
3. **Основной код** - использование URL из конфигурации вместо захардкоженных

## Dev Server

### Запуск dev сервера

```bash
# Из корня проекта
go run ./cmd/dev-server/main.go

# Или скомпилировать и запустить
go build -o dev-server ./cmd/dev-server
./dev-server
```

Сервер запускается на порту 8080 и предоставляет:

- `GET /api/v1/announcements` - API объявлений (эмулирует Upbit)
- `GET /v1/market/all` - API рынков (эмулирует Upbit)
- `POST /admin/add-notice` - добавить новое объявление
- `POST /admin/add-market` - добавить новый рынок
- `GET /admin/data` - посмотреть все данные
- `GET /health` - проверка здоровья

### Примеры использования dev сервера

#### Добавить новое объявление о листинге
```bash
curl -X POST http://localhost:8080/admin/add-notice \
  -H "Content-Type: application/json" \
  -d '{
    "category": "trade",
    "title": "Market Support for DOGE (Dogecoin) Trading",
    "symbol": "DOGE"
  }'
```

#### Добавить новый рынок
```bash
curl -X POST http://localhost:8080/admin/add-market \
  -H "Content-Type: application/json" \
  -d '{
    "market": "KRW-DOGE",
    "korean_name": "도지코인",
    "english_name": "Dogecoin"
  }'
```

#### Получить объявления
```bash
curl "http://localhost:8080/api/v1/announcements?page=1&per_page=20&category=trade"
```

#### Получить рынки
```bash
curl "http://localhost:8080/v1/market/all?isDetails=false"
```

## Конфигурация

### Структура конфигурации

В `config.json` переименованы и добавлены поля для лучшей консистентности:

**Переименования:**
- `news` → `announcements_api` (для работы с API объявлений)
- `pairs_diff` → `markets_api` (для работы с API рынков)
- Добавлен `service_center` (для работы с центром поддержки)

```json
{
  "settings": {
    "announcements_api": {
      "enabled": true,
      "cycle_interval_ms": 4800,
      "max_attempts": 8,
      "max_retry_after_sec": 100,
      "page_delay_jitter_ms": 100,
      "page_delay_ms": 1000,
      "proxy": true
    },
    "markets_api": {
      "enabled": true,
      "interval_ms": 4800,
      "max_attempts": 5,
      "proxy": true,
      "timeout_sec": 30
    },
    "service_center": {
      "enabled": true,
      "max_attempts": 3,
      "proxy": true,
      "timeout_sec": 30
    },
    "api": {
      "playground": "prod",
      "urls": {
        "dev": {
          "announcements_api": "http://localhost:8080/api/v1/announcements",
          "markets_api": "http://localhost:8080/v1/market/all",
          "service_center": "http://localhost:8080/service_center/notice"
        },
        "prod": {
          "announcements_api": "https://api-manager.upbit.com/api/v1/announcements",
          "markets_api": "https://api.upbit.com/v1/market/all",
          "service_center": "https://upbit.com/service_center/notice"
        }
      }
    }
  }
}
```

### Переключение между окружениями

#### Использовать dev сервер
```json
{
  "settings": {
    "api": {
      "playground": "dev"
    }
  }
}
```

#### Использовать продакшн API
```json
{
  "settings": {
    "api": {
      "playground": "prod"
    }
  }
}
```

## Workflow для разработки

### 1. Запуск dev окружения

```bash
# Запустить dev сервер
./dev-server

# В другом терминале переключиться на dev режим
# Изменить в config.json: "playground": "dev"

# Запустить основное приложение
./upbit-listing
```

### 2. Тестирование новых листингов

```bash
# Добавить новое объявление
curl -X POST http://localhost:8080/admin/add-notice \
  -H "Content-Type: application/json" \
  -d '{
    "category": "trade",
    "title": "Market Support for TEST (TestCoin) Trading"
  }'

# Добавить новый рынок
curl -X POST http://localhost:8080/admin/add-market \
  -H "Content-Type: application/json" \
  -d '{
    "market": "KRW-TEST",
    "korean_name": "테스트코인",
    "english_name": "TestCoin"
  }'
```

Основное приложение автоматически обнаружит новые данные и выполнит торговые операции.

### 3. Возврат к продакшн

```bash
# Изменить в config.json: "playground": "prod"
# Перезапустить основное приложение
```

## Технические детали

### Изменения в коде

1. **config.go** - переименованы структуры: `NewsConfig` → `AnnouncementsAPIConfig`, `PairsDiffConfig` → `MarketsAPIConfig`
2. **config.go** - добавлена структура `ServiceCenterConfig` для настроек центра поддержки
3. **config.go** - добавлены структуры `APIConfig`, `URLConfig`, `URLEndpoints` для управления URL
4. **config.go** - добавлены методы `GetCurrentURLs()`, `GetAnnouncementsAPI()`, `GetMarketsAPI()`, `GetServiceCenterURL()`
5. **main.go** - заменены захардкоженные URL на использование конфигурации
6. **main.go** - добавлена функция `getServiceCenterURL()` для генерации URL
7. **main.go** - добавлена логика автоматического отключения proxy и interval tuning в dev режиме
8. **main.go** - обновлены все ссылки на старые поля конфигурации на новые названия

### Автоматическая оптимизация для dev режима

В dev режиме (`playground: "dev"`) автоматически отключаются:

1. **Proxy соединения** - не нужны для localhost, все запросы идут напрямую
2. **Interval tuning** - нет rate limits на локальном сервере

Это обеспечивает:
- Более быструю работу в dev режиме
- Отсутствие ошибок подключения к proxy при работе с localhost
- Упрощенную отладку без лишних компонентов

### Fallback механизм

Если конфигурация не загружена, используются fallback URL:
- Announcements API: `https://api-manager.upbit.com/api/v1/announcements`
- Markets API: `https://api.upbit.com/v1/market/all`
- Service Center: `https://upbit.com/service_center/notice`

### Совместимость

- Все существующие функции работают без изменений
- Обратная совместимость с конфигурациями без новых полей
- Автоматическое определение окружения на основе настройки `playground`

## Преимущества

1. **Изолированная разработка** - можно тестировать без влияния на продакшн
2. **Контролируемые тесты** - можно создавать точные сценарии листинга
3. **Быстрая итерация** - не нужно ждать реальных листингов
4. **Безопасность** - торговые операции выполняются с тестовыми данными
5. **Гибкость** - легкое переключение между окружениями
6. **Автоматическая оптимизация** - система сама отключает ненужные компоненты в dev режиме
7. **Простота отладки** - в dev режиме работает только direct connection без proxy и interval tuning
