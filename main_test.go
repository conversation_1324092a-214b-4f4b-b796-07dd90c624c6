package main

import (
	"encoding/json"
	"fmt"
	"os"
	"testing"
	"time"
)

// Test that a new Upbit notice with listing title is recognized as listing and symbol extracted
func TestListingDetectionAndSymbolExtraction(t *testing.T) {
	sample := `{"listed_at":"2025-08-12T16:30:00+09:00","first_listed_at":"2025-08-12T16:30:00+09:00","id":5409,"title":"Market Support for Solana (SOL) (KRW, USDT Market)","category":"Trade","need_new_badge":false,"need_update_badge":false}`
	var n notice
	if err := json.Unmarshal([]byte(sample), &n); err != nil {
		t.Fatalf("failed to unmarshal notice: %v", err)
	}

	if !isTradeCategory(n.Category) {
		t.Fatalf("expected category Trade to be recognized as trade")
	}

	// simulate extract as done in fetchAllNew
	title := normalizeTitle(n.Title)
	sym := extractSymbol(title)
	if sym != "SOL" {
		t.Fatalf("expected symbol SOL, got %q", sym)
	}

	if !isListingTitle(title) {
		t.Fatalf("expected isListingTitle to be true for title %q", title)
	}

	// verify timestamp parsing and fillDateStrings
	parsed := parseKST(n.ListedAt)
	if parsed.IsZero() {
		t.Fatalf("expected parseKST to succeed")
	}
	it := cacheItem{Timestamp: parsed.UnixMilli()}
	fillDateStrings(&it)
	if it.DateUTC == "" || it.DateKST == "" || it.DateMSK == "" {
		t.Fatalf("expected date strings to be filled: %+v", it)
	}
}

// Test that Korean category text is treated as trade as well
func TestIsTradeCategory_Korean(t *testing.T) {
	if !isTradeCategory("거래") {
		t.Fatalf("expected Korean 거래 to be recognized as trade category")
	}
}

// Test backfill/normalization of timestamps
func TestBackfillTimestamps_Normalization(t *testing.T) {
	// seconds to ms normalization
	secTs := time.Now().Unix() // seconds
	items := []cacheItem{{Timestamp: secTs}}
	updated := backfillTimestamps(items)
	if updated == 0 || items[0].Timestamp < 1_000_000_000_000 {
		t.Fatalf("expected timestamp to be normalized to ms, got %d (updated=%d)", items[0].Timestamp, updated)
	}
}

// Test trading flow emits expected events in test mode without hitting real exchanges
func TestTradingFlow_TestHarness(t *testing.T) {
	// prepare one listing item
	items := []cacheItem{{
		Category:  "Trade",
		Title:     "Market Support for Solana (SOL) (KRW, USDT Market)",
		Symbol:    "SOL",
		Listing:   true,
		Timestamp: time.Now().UnixMilli(),
	}}

	// enable test trading mode and configure single TP/SL
	os.Setenv("UPBIT_TEST_TRADING", "1")
	tradeEnabled = true
	tradeOnBybit = true
	tradeOnBingx = true
	orderDirection = "long"
	orderAmountUSDT = 50
	orderLeverage = 5
	orderTPPercents = []float64{1}
	orderSLPercents = []float64{1}
	testTradeEvents = nil

	if err := handleTradingOnNewListings(items); err != nil {
		t.Fatalf("handleTradingOnNewListings error: %v", err)
	}

	// Expect MARKET,TP,SL events for both exchanges in some order
	wantAny := map[string]bool{
		"bybit:MARKET:Buy": true,
		"bingx:MARKET:Buy": true,
		"bybit:TP":         true,
		"bybit:SL":         true,
		"bingx:TP":         true,
		"bingx:SL":         true,
	}
	seen := make(map[string]bool)
	for _, ev := range testTradeEvents {
		seen[ev] = true
	}
	for k := range wantAny {
		if !seen[k] {
			t.Fatalf("expected test event %q, got %#v", k, testTradeEvents)
		}
	}
}

// Test that Telegram message formatting includes diff calculation
func TestTelegramMessageFormatting_WithDiff(t *testing.T) {
	// Mock account config
	acc := AccountConfig{
		Name:             "test-account",
		TelegramEnabled:  true,
		TelegramLogsChat: 123456,
		TelegramBotToken: "test-token",
		TrailingEnabled:  true,
		TrailingPercent:  20.0,
	}

	// Mock timing values
	signalFoundTime := int64(*************)
	orderSentTime := int64(*************)
	orderConfirmedTime := int64(*************)
	expectedDiff := orderConfirmedTime - orderSentTime // 632 ms

	// Mock other values
	symbol := "DOGEUSDT"
	side := "LONG"
	qty := 86.0
	leverage := 20
	tpList := []float64{10}
	slList := []float64{10}
	trailingInfo := " TRAIL=20.0%"

	// Format message as done in the actual code
	orderDiff := orderConfirmedTime - orderSentTime
	text := fmt.Sprintf("%s | %s %s qty=%.6f lev=%dx TP=%v SL=%v%s (signal: %d ms, sent: %d ms, confirmed: %d ms, diff: %d ms)",
		acc.Name, symbol, side, qty, leverage, tpList, slList, trailingInfo, signalFoundTime, orderSentTime, orderConfirmedTime, orderDiff)

	expectedText := "test-account | DOGEUSDT LONG qty=86.000000 lev=20x TP=[10] SL=[10] TRAIL=20.0% (signal: ************* ms, sent: ************* ms, confirmed: ************* ms, diff: 632 ms)"

	if text != expectedText {
		t.Fatalf("expected message:\n%s\ngot:\n%s", expectedText, text)
	}

	// Verify diff calculation
	if orderDiff != expectedDiff {
		t.Fatalf("expected diff %d ms, got %d ms", expectedDiff, orderDiff)
	}
}
