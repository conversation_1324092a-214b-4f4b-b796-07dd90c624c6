# Trailing Stop Functionality

## Описание

Добавлена функциональность trailing stop (трейлинг стоп) в торговый бот для автоматического управления позициями. Trailing stop позволяет автоматически перемещать stop loss в прибыльном направлении при движении цены, защищая прибыль и минимизируя потери.

## Как работает Trailing Stop

### Для Long позиций:
- При росте цены выше максимума, stop loss автоматически поднимается
- Stop loss устанавливается на заданный процент ниже текущей максимальной цены
- При падении цены stop loss остается на месте

### Для Short позиций:
- При падении цены ниже минимума, stop loss автоматически опускается  
- Stop loss устанавливается на заданный процент выше текущей минимальной цены
- При росте цены stop loss остается на месте

## Конфигурация

### Настройки в config.json

Добавлены новые поля в конфигурацию аккаунта:

```json
{
  "accounts": [
    {
      "exchange": "bybit",
      "name": "suenot: bybit",
      "enabled": true,
      "api_key": "${BYBIT_API_KEY}",
      "api_secret": "${BYBIT_API_SECRET}",
      "direction": "long",
      "amount_usdt": 50,
      "leverage": 10,
      "tp": [10],
      "sl": [10],
      "trailing_enabled": true,
      "trailing_percent": 20,
      "telegram_enabled": true,
      "telegram_logs_chat": -**********,
      "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}"
    }
  ]
}
```

### Параметры:

- **`trailing_enabled`** (bool): Включить/выключить trailing stop для аккаунта
- **`trailing_percent`** (float): Процент для trailing stop (например, 20 = 20%)

## Пример работы

### Сценарий для Long позиции с trailing_percent = 20%:

1. **Вход**: Цена $100, Stop Loss $90 (из настройки sl: [10])
2. **Цена $110**: Новый максимум! Stop Loss обновляется до $88 (20% ниже $110)
3. **Цена $120**: Новый максимум! Stop Loss обновляется до $96 (20% ниже $120)
4. **Цена $130**: Новый максимум! Stop Loss обновляется до $104 (20% ниже $130)
5. **Цена $125**: Цена падает, но Stop Loss остается $104
6. **Цена $104**: Stop Loss срабатывает, позиция закрывается с прибылью 4%

## Технические детали

### Мониторинг
- Проверка цен каждые 5 секунд
- Автоматическое обновление stop loss через API биржи
- Логирование всех изменений

### Поддерживаемые биржи
- **Bybit**: Полная поддержка через API v5
- **BingX**: Поддержка через conditional orders

### Безопасность
- Stop loss может только улучшаться (двигаться в прибыльном направлении)
- При ошибках API позиция остается защищенной предыдущим stop loss
- Все операции логируются

## Telegram уведомления

В сообщения добавлена информация о trailing stop:

```
suenot: bybit | API3USDT LONG qty=0.500000 lev=10x TP=[10] SL=[10] TRAIL=20.0% (signal: 1640995200000 ms, sent: 1640995200100 ms, confirmed: 1640995200200 ms)
```

## Логи

Примеры логов trailing stop:

```
trailing: added bybit API3USDT position for long, entry=1.234500, trailing=20.00%
trailing: updated bybit API3USDT SL from 0.987600 to 1.123400 (price: 1.404250)
```

## Тестирование

Функциональность протестирована в тестовом режиме:

```bash
./upbit-listing test-trading
```

## Рекомендации по использованию

### Настройка процента:
- **Консервативно**: 10-15% для стабильных активов
- **Умеренно**: 15-25% для волатильных активов  
- **Агрессивно**: 25-35% для высоковолатильных активов

### Комбинирование с TP/SL:
- Используйте базовый SL как страховку
- Trailing stop работает дополнительно к основным TP/SL
- Рекомендуется устанавливать базовый SL ниже trailing процента

## Ограничения

- Работает только для новых позиций (открытых после включения функции)
- Требует стабильное интернет-соединение для мониторинга
- Зависит от доступности API биржи
- Не работает в тестовом режиме (UPBIT_TEST_TRADING=1)

## Отключение

Для отключения trailing stop установите:
```json
"trailing_enabled": false
```

Или удалите поля `trailing_enabled` и `trailing_percent` из конфигурации.
