{"accounts": [{"amount_usdt": 20, "api_key": "${BYBIT_API_KEY}", "api_secret": "${BYBIT_API_SECRET}", "direction": "long", "enabled": true, "exchange": "bybit", "leverage": 20, "name": "suenot: bybit", "sl": [10], "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}", "telegram_enabled": true, "telegram_logs_chat": -**********, "tp": [10], "trailing_enabled": true, "trailing_percent": 10}], "proxies": ["socks5:************:63969:VZMp5PKM:53BYByBB"], "settings": {"api": {"playground": "dev", "urls": {"dev": {"announcements_api": "http://localhost:8080/api/v1/announcements", "markets_api": "http://localhost:8080/v1/market/all", "service_center": "http://localhost:8080/service_center/notice"}, "prod": {"announcements_api": "https://api-manager.upbit.com/api/v1/announcements", "markets_api": "https://api.upbit.com/v1/market/all", "service_center": "https://upbit.com/service_center/notice"}}}, "cycle": true, "interval_tuning": {"check_period_minutes": 60, "enabled": false, "max_interval_ms": 60000, "max_rollback_count": 10, "min_interval_ms": 1000, "tune_step_ms": 100}, "announcements_api": {"cycle_interval_ms": 5000, "enabled": true, "max_attempts": 8, "max_retry_after_sec": 100, "page_delay_jitter_ms": 100, "page_delay_ms": 1000, "proxy": false, "forced_parsing": {"enabled": true, "before_ms": 30000, "after_ms": 30000, "request_interval_ms": 1000}}, "markets_api": {"enabled": true, "interval_ms": 5000, "max_attempts": 5, "proxy": false, "timeout_sec": 30, "forced_parsing": {"enabled": true, "before_ms": 30000, "after_ms": 30000, "request_interval_ms": 1000}}, "service_center": {"enabled": true, "interval_ms": 5000, "max_attempts": 3, "proxy": false, "timeout_sec": 30, "forced_parsing": {"enabled": true, "before_ms": 30000, "after_ms": 30000, "request_interval_ms": 1000}}, "trade": false}, "telegram_signals": {"enabled": true, "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}", "telegram_signals_chat": -4952846534}}